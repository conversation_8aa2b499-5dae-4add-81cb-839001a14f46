# Project Goal: Migrate to Colyseus

The primary objective is to refactor the application's architecture by migrating the core real-time game logic from a stateless **Firebase Cloud Functions** backend to a stateful **Colyseus** game server.

## Key Architectural Changes:

1.  **State Management:** Move from a stateless model where game state is read from/written to Firestore on every action, to a stateful model where the game state is held in memory within a Colyseus `Room` for the duration of a match.
2.  **Logic Centralization:** Consolidate the game logic, currently spread across multiple cloud functions in the `functions/` directory, into the `colyseus-server/src/jeopardy_room.ts` class and its associated handlers.
3.  **Communication Protocol:** Shift from an HTTPS-based request/response model between the client and backend to a persistent WebSocket connection managed by Colyseus. This enables more efficient, low-latency, real-time communication via state patching.
4.  **Database Role:** Firestore's role will transition from being the primary real-time data store to a persistence layer used for:
    *   Loading a room's state when it's first created (to support re-joining games).
    *   Saving the final game state and scores at the end of a match.

# Memory & Project Facts

This document stores key facts about the project's structure, technologies, and conventions to ensure consistent and efficient work.

## Directory Structure

-   `/`: The root of the Angular frontend application.
-   `colyseus-server/`: Contains the Colyseus game server, which is the focus of the current migration.
    -   `colyseus-server/src/`: The source code for the game server.
    -   `colyseus-server/src/jeopardy_room.ts`: The central class for the game room logic.
    -   `colyseus-server/src/handlers/`: Contains handler functions for specific game actions (e.g., `buzz_in_handler.ts`, `select_clue_handler.ts`).
-   `functions/`: Contains the legacy Firebase Cloud Functions that are being replaced.
-   `src/`: The source code for the Angular frontend application.

## Core Technologies

-   **Frontend:** Angular
-   **Real-time Game Server:** Colyseus
-   **Database / Legacy Backend:** Firebase (Firestore, Cloud Functions)
-   **Testing:** Jest, `ts-jest`, `esbuild-jest`

## Key Commands

-   **Run Tests:** `npm test` (executed from within the `colyseus-server/` directory).

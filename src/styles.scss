@use "../node_modules/@angular/material" as mat;
@use "_theme-colors" as my-theme;

@include mat.core(); // You need this only once in your app.

$primary-palette: my-theme.$primary-palette;
$tertiary-palette: my-theme.$tertiary-palette;

html {
  color-scheme: dark;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  @include mat.theme(
    (
      color: (
        primary: $primary-palette,
        tertiary: $tertiary-palette
      ),
      typography: Roboto,
      density: 0
    )
  );
}

body {
  margin: 0px;
  // Old browsers won't support 100dvh.
  height: 100vh;
  height: 100dvh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

*.dark-mode {
  color-scheme: dark;
}

*.light-mode {
  color-scheme: light;
  --csstools-color-scheme--light: initial;
}

* {
  transition: background-color 0.3s ease, transform 0.3s ease,
    box-shadow 0.3s ease;
  box-sizing: border-box; /* Ensure all elements use border-box */
}

/* Define reusable animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* Style for info icons */
.info-icon {
  font-size: 1rem;
  height: 1rem;
  width: 1rem;
  vertical-align: middle;
  margin-left: 0.25rem;
  opacity: 0.7;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.info-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Add gradient animation for buttons */
.gradient-button {
  background: linear-gradient(
    45deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add subtle hover effects for interactive elements */
button:not(:disabled),
.interactive {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

button:not(:disabled):hover,
.interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tertiary-colors {
  @include mat.theme(
    (
      color: $tertiary-palette
    )
  );
}

html {
  background-color: var(--mat-sys-surface);
}

.firebase-emulator-warning {
  display: none !important;
}
.emoji {
  font-size: 3rem;
  display: inline-block;
  letter-spacing: 0.3em;
  margin-right: -0.3em;
}
/*
  1. This makes the Plyr container a positioning context
     for our overlay.
*/
.plyr {
  position: relative;
}

/*
  2. This creates the overlay itself. It's a pseudo-element
     that covers the entire container, but it's hidden by default.
*/
.plyr--video::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent; /* Makes the overlay invisible */
  z-index: 10000; /* Ensures it's on top of the iframe */
  display: none; /* Hidden by default */
}

/*
  3. This is the magic rule. When Plyr adds the .plyr--paused
     class to the container, we make our overlay visible.
*/
.plyr--video.plyr--paused::after {
  display: block;
  background: black;
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CastComponent } from './cast.component';
import { CastService } from '../services/cast.service';
import { of } from 'rxjs';
import { cast } from '../test/cast.mock';

// Mock the cast object
(window as any).cast = cast;

describe('CastComponent', () => {
  let component: CastComponent;
  let fixture: ComponentFixture<CastComponent>;
  let castServiceSpy: jasmine.SpyObj<CastService>;

  beforeEach(async () => {
    castServiceSpy = jasmine.createSpyObj('CastService', [
      'getCastContext',
      'sessionStateEventData$'
    ]);
    castServiceSpy.sessionStateEventData$ = of(null);

    await TestBed.configureTestingModule({
      imports: [CastComponent],
      providers: [{ provide: CastService, useValue: castServiceSpy }]
    }).compileComponents();

    fixture = TestBed.createComponent(CastComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should cast media', async () => {
    const sendMessageSpy = jasmine.createSpy('sendMessage');
    component.castSession = {
      sendMessage: sendMessageSpy
    };
    component.url = 'test-url';
    await component.castMedia();
    expect(sendMessageSpy).toHaveBeenCalledWith('urn:x-cast:es.offd.dashcast', {
      url: 'test-url',
      force: false,
      reload: false,
      reload_time: 0
    });
  });

  it('should show cast button if devices are available', () => {
    castServiceSpy.getCastContext.and.returnValue({
      getCastState: () => cast.framework.CastState.NOT_CONNECTED
    } as any);
    expect(component.shouldShowCast()).toBe(true);
  });

  it('should not show cast button if no devices are available', () => {
    castServiceSpy.getCastContext.and.returnValue({
      getCastState: () => cast.framework.CastState.NO_DEVICES_AVAILABLE
    } as any);
    expect(component.shouldShowCast()).toBe(false);
  });
});
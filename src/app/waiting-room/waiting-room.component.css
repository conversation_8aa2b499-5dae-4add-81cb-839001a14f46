:host {
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: radial-gradient(
    rgba(255, 255, 255, 0.05) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
  animation: fadeIn 0.5s ease-in-out;
  overflow-y: hidden;
}

.streaming-response {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--mat-sys-surface-container-low);
  border: 1px solid var(--mat-sys-outline);
  color: var(--mat-sys-on-surface-variant);
  padding: 10px;
  margin-top: 10px;
  white-space: pre-wrap;
  font-family: monospace;
  transition: filter 0.5s ease-in-out;
  scroll-behavior: smooth;
  text-align: left;
  max-width: 100%;
  user-select: none;
}
.category-progress-container {
  width: 100%;
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-progress-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.category-name {
  flex: 1;
  font: var(--mat-sys-body-large);
  color: var(--mat-sys-on-surface-variant);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-progress-item mat-progress-bar {
  flex: 2;
}

.category-percentage {
  flex: 0 0 40px;
  text-align: right;
  font: var(--mat-sys-body-large);
  color: var(--mat-sys-primary);
}

.blurred {
  filter: blur(3px);
}

.waiting-room-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 90%;
  animation: slideInUp 0.5s ease-out;
  overflow-y: auto;
  max-height: 100%;
}

.room-title {
  font: var(--mat-sys-headline-medium);
  color: var(--mat-sys-on-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(
    135deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  position: relative;
  letter-spacing: 0.02em;
  flex: 1;
}

/* When loading or generating questions, center the room code */
.room-title.loading-state {
  justify-content: center;
}

.room-code {
  display: inline-block;
}

.player-list-card {
  width: 100%;
  background-color: var(--mat-sys-primary-container);
  border-radius: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex-direction: unset;
  min-height: 7rem;
  align-items: center;
  overflow: auto;
  /* Removed hover animation */
}

.player-list-card mat-card-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem 0.5rem;
}

.player-list-card .mat-mdc-card-title {
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.players-container {
  display: flex;
  flex-wrap: wrap; /* Allow players to wrap to the next line */
  justify-content: center; /* Center players horizontally */
  gap: 1rem; /* Spacing between player items */
}
.players-list {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  gap: 2rem;
}

.player-list-card * {
  color: var(--mat-sys-on-primary-container);
}

.mat-mdc-card-content:last-child {
  padding-bottom: 0;
}

.admin-setup,
.read-only-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding: 1.25rem; */
  height: 0%;
}

/* Card header styling for vertical alignment */
.admin-setup mat-card-header,
.read-only-card mat-card-header {
  display: flex;
  align-items: center;
}

.admin-setup .mat-mdc-card-title,
.read-only-card .mat-mdc-card-title {
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

/* Ensure all text elements in admin-setup and read-only cards use the correct color */
.admin-setup .mat-mdc-card-title,
.admin-setup .mat-mdc-card-subtitle,
.admin-setup .mat-mdc-card-content,
.admin-setup .timer-label,
.admin-setup mat-label,
.admin-setup .mat-mdc-select-value,
.admin-setup .mat-mdc-select-arrow,
.admin-setup .mat-mdc-option-text,
.read-only-card .mat-mdc-card-title,
.read-only-card .mat-mdc-card-subtitle,
.read-only-card .mat-mdc-card-content,
.read-only-card .read-only-label,
.read-only-card .read-only-value {
  color: var(--mat-sys-on-primary-container);
}

/* Read-only list styles */
.read-only-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.75rem;
}

.read-only-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(var(--mat-sys-on-primary-container-rgb), 0.1);
  font: var(--mat-sys-body-large);
}

.read-only-label {
  font-weight: 500;
  margin-right: 1rem;
  min-width: 8rem;
  font: var(--mat-sys-body-large);
}

.read-only-value {
  flex-grow: 1;
  text-align: right;
  font: var(--mat-sys-body-large);
}

.read-only-empty-message {
  font-style: italic;
  opacity: 0.7;
  text-align: center;
  padding: 1rem 0;
  font: var(--mat-sys-body-large);
}

.category-input-list {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.player-username {
  font: var(--mat-sys-body-large);
}

.vertical-list-mat-card-content {
  width: 100%;
  padding-top: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  max-width: 80rem;
  padding: 1rem;
  border-radius: 1rem;
  background-color: var(--mat-sys-surface-container-high);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  margin: 1rem 0;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);

  gap: 1rem;
  /* Add subtle gradient background */
  background-image: linear-gradient(
    135deg,
    rgba(var(--mat-sys-primary-rgb), 0.05) 0%,
    rgba(var(--mat-sys-tertiary-rgb), 0.05) 100%
  );
}

/* Add animated particles in the background */
.loading-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 30%,
      rgba(var(--mat-sys-primary-rgb), 0.15) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 75% 20%,
      rgba(var(--mat-sys-tertiary-rgb), 0.15) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 40% 80%,
      rgba(var(--mat-sys-primary-rgb), 0.15) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 80% 70%,
      rgba(var(--mat-sys-tertiary-rgb), 0.15) 0%,
      transparent 8%
    );
  background-size: 150% 150%;
  animation: particleMove 15s infinite ease-in-out alternate;
  z-index: 0;
  opacity: 0.7;
}

@keyframes particleMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20) translate(-50%, -50%);
    opacity: 0;
  }
}

.generation-progress-bar {
  width: 100%;
  height: 0.75rem;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Add animated glow to progress bar */
.generation-progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: progressGlow 2s infinite linear;
  z-index: 2;
}

@keyframes progressGlow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.progress-percentage {
  font: var(--mat-sys-headline-small);
  background: linear-gradient(
    135deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0;
  font-weight: 700;
  letter-spacing: 0.05em;
  font-size: 2rem;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.progress-message {
  font: var(--mat-sys-body-large);
  color: var(--mat-sys-on-surface);
  margin: 0;
  text-align: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  min-height: 3.5rem; /* Fixed height to prevent layout shifts */
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.progress-category {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-tertiary);
  margin: 0;
  text-align: center;
  font-style: italic;
  font-weight: 500;
  letter-spacing: 0.03em;
  padding: 0.5rem 1.5rem;
  border-radius: 1rem;
  background-color: rgba(var(--mat-sys-tertiary-rgb), 0.1);
  border: 1px solid rgba(var(--mat-sys-tertiary-rgb), 0.2);
  min-height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 1%;
  width: 100%;
  flex-wrap: wrap;
  row-gap: 0.625rem;
  padding-bottom: 6.25rem;
}
.categories-card {
  flex-grow: 2;
}
.team-setup-card {
  flex-grow: 1;
}
.other-settings-card {
  flex-grow: 1;
}

.timer-settings-card {
  flex-grow: 1;
}

.timer-settings-card .mat-mdc-card-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem 0.5rem;
}

.timer-settings-card .mat-mdc-card-header .mat-mdc-card-title {
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.timer-settings-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding: 0.5rem 1rem 1rem;
}

.timer-setting {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.timer-label {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
  font: var(--mat-sys-body-large);
}

.timer-label-modified {
  color: var(--mat-sys-primary);
  font-weight: 600;
}

/* Override for light mode to ensure visibility */
.light-theme .admin-setup .timer-label-modified {
  color: var(--mat-sys-primary-dark);
}

.info-icon {
  cursor: help;
  opacity: 0.7;
  margin-left: 0.5rem;
  font-size: 1em;
  height: 1em;
  width: 1em;
  position: relative;
  color: var(--mat-sys-on-primary-container);
}

.reset-button {
  margin-left: auto;
  color: var(--mat-sys-on-primary-container);
  align-self: center;
}

/* Ensure inputs and form fields have proper contrast in both themes */
.admin-setup .mat-mdc-text-field-wrapper,
.admin-setup .mat-mdc-form-field-infix,
.admin-setup input.mat-mdc-input-element {
  color: var(--mat-sys-on-primary-container);
}

mat-slider {
  transition: opacity 0.3s ease;
}

mat-slider:hover {
  opacity: 0.9;
}

.mat-form-field {
  width: 100%;
}

.top-row {
  display: flex;
  width: 100%;
  padding-top: 2%;
  justify-content: center;
}
.bottom-row {
  width: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
.share-and-room-code {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.question-manager-button {
  background-color: var(--mat-sys-secondary-container);
  color: var(--mat-sys-on-secondary-container);
  transition: all 0.3s ease;
}

.question-manager-button:hover {
  background-color: var(--mat-sys-secondary);
  color: var(--mat-sys-on-secondary);
  transform: scale(1.05);
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.start-game-button {
  --mdc-protected-button-container-height: 100%;
  border-radius: 2rem;
  font: var(--mat-sys-body-large);
  padding: 0.25rem 1rem;
  background: linear-gradient(
    45deg,
    var(--mat-sys-tertiary),
    var(--mat-sys-tertiary-container)
  );
  margin-left: auto;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mat-sys-on-tertiary);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 4px 15px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  margin-top: 0;
  align-self: center;
}

.start-game-button .play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
}

.start-game-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    var(--mat-sys-tertiary),
    var(--mat-sys-tertiary-container)
  );
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  z-index: 0;
  opacity: 0.7;
}

.start-game-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px
    color-mix(in srgb, var(--mat-sys-shadow) 40%, transparent);
}

.start-game-button:active:not(:disabled) {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 2px 10px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
}

.start-game-button:disabled {
  background: color-mix(
    in srgb,
    var(--mat-sys-tertiary) 30%,
    var(--mat-sys-surface-container-high)
  );
  color: var(--mat-sys-on-surface-variant);
  box-shadow: none;
  border: 1px solid var(--mat-sys-outline-variant);
  cursor: not-allowed;
  opacity: 0.7;
  position: relative;
  animation: none;
}

.start-game-button:disabled::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(var(--mat-sys-tertiary-rgb), 0.1) 10px,
    rgba(var(--mat-sys-tertiary-rgb), 0.1) 20px
  );
  z-index: 1;
  animation: none;
}

.start-game-button:disabled .mat-icon,
.start-game-button:disabled span {
  opacity: 0.7;
}

.start-game-button .mat-icon,
.start-game-button span {
  position: relative;
  z-index: 1;
}

.player-icon-and-name {
  text-align: center;
  height: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.full-width-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.full-width {
  width: 100%;
  max-width: 100%;
}

app-category-wizard,
app-category-selector {
  width: 100%;
}

/* Styling for read-only category selector */
.read-only-selector {
  opacity: 0.9;
  pointer-events: none; /* Disable interactions */
}

/* Read-only indicator styling */
.read-only-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  margin-top: 0.5rem;
  background-color: rgba(var(--mat-sys-info-rgb), 0.1);
  border-radius: 0.5rem;
  font-size: var(--mat-sys-body-small);
  color: var(--mat-sys-on-surface);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.read-only-indicator mat-icon {
  font-size: 1rem;
  height: 1rem;
  width: 1rem;
  color: var(--mat-sys-info);
}
.player-icon {
  vertical-align: middle;
  border-radius: 100%;
  height: 3rem;
  width: 3rem;
}

/* TV-specific styles */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .waiting-room-container {
    gap: 0.5rem;
    padding: 0.5rem 0;
    display: flex;
    flex-direction: column;
  }

  .room-title {
    font: var(--mat-sys-headline-small);
    padding: 0.4rem 0.75rem;
  }
}

/* Cast mode specific styles */
.is-cast {
  /* Global cast mode styles */
  --cast-card-margin: 0.25rem;
  --cast-card-padding: 0.5rem;
  --cast-border-radius: 0.5rem;
  --cast-gap: 0.25rem;
}

.is-cast .waiting-room-container {
  display: flex;
  flex-direction: column;
  gap: var(--cast-gap);
  padding: 0.5rem 0;
  width: 98%;
  height: 100%;
  justify-content: space-between;
}

.is-cast .room-title {
  font-size: clamp(1rem, 3vw, 1.5rem);
  padding: 0.3rem 0.5rem;
}

.is-cast .top-row {
  flex: 0 0 auto;
}

/* Make category selector take minimal space */
.is-cast app-category-selector {
  flex: 0 0 auto;
  margin: 0 auto;
}

/* Make player list and game settings take available space */
.is-cast .player-list-card,
.is-cast .admin-setup,
.is-cast .read-only-card {
  margin: var(--cast-card-margin) 0;
  border-radius: var(--cast-border-radius);
}

.is-cast .player-list-card {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.is-cast .player-list-card mat-card-content {
  flex: 1;
  overflow: auto;
}

.is-cast .players-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: var(--cast-gap);
  padding: var(--cast-card-padding);
}

.is-cast .player-icon-and-name {
  flex: 0 1 auto;
  padding: 0.25rem 0.5rem;
}

/* Loading container */
.is-cast .loading-container {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--cast-card-padding);
  margin: var(--cast-card-margin) 0;
  border-radius: var(--cast-border-radius);
}

.is-cast .progress-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.is-cast .progress-percentage {
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.is-cast .progress-message,
.is-cast .progress-category {
  font-size: clamp(0.8rem, 2vw, 1rem);
  text-align: center;
}

/* Start game button */
.is-cast .start-game-button {
  padding: 0.25rem 0.75rem;
  font-size: clamp(0.8rem, 1.5vw, 1rem);
  border-radius: 1.5rem;
  margin-left: 0.5rem;
}

/* Fix for Material icons in cast mode */
.is-cast .mat-icon {
  height: auto !important;
  width: auto !important;
}

/* Remove all tooltips in cast mode */
.is-cast [matTooltip] {
  pointer-events: none;
}

/* Mobile styles */
@media screen and (max-width: 600px) {
  .header-container {
    gap: 0.5rem;
    padding: 0 0.25rem;
  }

  .room-title {
    font: var(--mat-sys-headline-small);
    padding: 0.3rem 0.5rem;
  }

  /* Hide start game button text on small screens */
  .start-game-button .button-text {
    display: none;
  }

  .start-game-button {
    padding: 0;
    min-width: 0;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .start-game-button .play-icon {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    font-size: 32px;
    line-height: 32px;
  }
}

.current-player-box {
  /* Use the theme's primary color for the border */
  border: 2px solid var(--mat-sys-primary);
  border-radius: 5px;
  margin: 2px 0;
  /* Use the primary container color for a subtle background highlight */
  background-color: var(--mat-sys-primary-container);
  /* Ensure the text color contrasts with the new background */
  color: var(--mat-sys-on-primary-container);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
}

<div class="waiting-room-container" [class.is-cast]="isCast">
  <div class="top-row">
    <div class="header-container">
      <button
        mat-fab
        (click)="copyLinkToClipboard()"
        class="share-button tertiary-colors"
        [matTooltip]="
          isGamemasterMode ? 'Share Cast Room Link' : 'Share Lobby Link'
        "
      >
        <mat-icon>share</mat-icon>
      </button>
      <div
        class="room-title"
        [class.loading-state]="loading || isGeneratingQuestions"
      >
        <span class="room-code">Waiting Room: {{ room!.roomId }}</span>
      </div>
      <div
        class="header-actions"
        *ngIf="!loading && !isGeneratingQuestions && isAdmin"
      >
        <!-- <button
          mat-fab
          (click)="goToQuestionManager()"
          class="question-manager-button"
          matTooltip="Manage Questions"
        >
          <mat-icon>quiz</mat-icon>
        </button> -->
        <button
          mat-fab
          extended
          (click)="onStartRoom()"
          class="start-game-button"
          [disabled]="isStartButtonDisabled()"
          [matTooltip]="getStartButtonTooltip()"
        >
          <mat-icon class="play-icon">play_arrow</mat-icon>
          <span class="button-text">Start Game</span>
        </button>
      </div>
    </div>
  </div>
  <div *ngIf="loading || isGeneratingQuestions" class="loading-container">
    <mat-progress-bar
      mode="determinate"
      [value]="generationProgress"
    ></mat-progress-bar>
    <p class="progress-percentage">{{ generationProgress }}% Complete</p>
    <div
      #streamingContainer
      class="streaming-response"
      [class.blurred]="!generationCompleted"
      *ngIf="streamedResponse$ | async as streamedResponse"
    >
      {{ streamedResponse }}
    </div>
    <div class="progress-text">
      <!-- <p class="progress-message">{{ currentGenerationMessage }}</p> -->
      <p *ngIf="generatingCategory" class="progress-category">
        Category: {{ generatingCategory }}
      </p>
    </div>
    <div class="category-progress-container">
      <div
        *ngFor="let category of categories; let i = index"
        class="category-progress-item"
      >
        <span class="category-name">{{ category }}</span>
        <mat-progress-bar
          mode="determinate"
          [value]="getCategoryProgress(i)"
        ></mat-progress-bar>
        <span class="category-percentage"
          >{{ getCategoryProgress(i) | number: "1.0-0" }}%</span
        >
      </div>
    </div>
  </div>
  <mat-card
    *ngIf="!loading && !isGeneratingQuestions && !isGamemasterMode"
    class="player-list-card"
  >
    <mat-card-header>
      <mat-card-title
        >Players
        <mat-icon
          class="info-icon"
          matTooltip="Players who have joined this game session"
          >people</mat-icon
        ></mat-card-title
      >
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="room" class="players-list">
        <mat-list-item
          *ngFor="let player of room!.playerIds; let pIdx = index"
          class="player-icon-and-name"
          [class.current-player-box]="player === currentUserId"
        >
          <mat-icon
            *ngIf="!room!.playerInfos[pIdx].photoUrl"
            matListItemIcon
            class="player-icon"
            >person</mat-icon
          >
          <img
            *ngIf="room!.playerInfos[pIdx].photoUrl"
            [src]="room!.playerInfos[pIdx].photoUrl"
            matListItemIcon
            class="player-icon"
          />
          <div class="player-username" matListItemTitle>
            {{ room!.playerInfos[pIdx].userName }}
          </div>
        </mat-list-item>
      </div>
    </mat-card-content>
  </mat-card>
  <div class="bottom-row">
    <div class="full-width-container">
      <!-- Show category selector to everyone, but only admin can add categories -->
      <app-category-selector
        *ngIf="!loading && !isGeneratingQuestions"
        (emitSubCategories)="isAdmin ? onEmitSubCategory($event) : null"
        class="full-width"
        [isReadOnly]="!isAdmin"
        [isCast]="isCast"
      ></app-category-selector>
    </div>

    <div class="settings-container">
      <!-- Admin editable categories card -->
      <mat-card
        *ngIf="this.isAdmin && !loading && !isGeneratingQuestions"
        class="admin-setup categories-card"
      >
        <mat-card-header>
          <mat-card-title
            >Categories
            <mat-icon
              class="info-icon"
              matTooltip="Add categories for questions to be generated from"
              >category</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <app-dynamic-list-input
            [listLabel]="'Category'"
            (listChangeEvent)="onUpdateCategories($event)"
            #dynamicList
          >
          </app-dynamic-list-input>
        </mat-card-content>
      </mat-card>

      <!-- Non-admin read-only categories card -->
      <mat-card
        *ngIf="
          (!this.isAdmin || this.isCast) &&
          !loading &&
          !isGeneratingQuestions &&
          categories.length > 0
        "
        class="read-only-card categories-card"
      >
        <mat-card-header>
          <mat-card-title
            >Categories
            <mat-icon
              class="info-icon"
              matTooltip="Categories selected for this game"
              >visibility</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <div class="read-only-list">
            <div
              *ngFor="let category of categories; let i = index"
              class="read-only-item"
            >
              <div class="read-only-label">Category {{ i + 1 }}</div>
              <div class="read-only-value">{{ category }}</div>
            </div>
            <div
              *ngIf="categories.length === 0"
              class="read-only-empty-message"
            >
              No categories added yet
            </div>
          </div>
        </mat-card-content>
      </mat-card>
      <!-- Admin editable teams card -->
      <mat-card
        *ngIf="
          isAdmin && !loading && !isGeneratingQuestions && isGamemasterMode
        "
        class="admin-setup team-setup-card"
      >
        <mat-card-header>
          <mat-card-title>
            Setup Teams
            <mat-icon
              class="info-icon"
              matTooltip="Add teams for gamemaster mode"
              >groups</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <app-dynamic-list-input
            [listLabel]="'Team'"
            (listChangeEvent)="onUpdateTeams($event)"
            #teamsList
          >
          </app-dynamic-list-input>
        </mat-card-content>
      </mat-card>

      <!-- Non-admin read-only teams card -->
      <mat-card
        *ngIf="
          !isAdmin &&
          !loading &&
          !isGeneratingQuestions &&
          isGamemasterMode &&
          teams.length > 0
        "
        class="read-only-card team-setup-card"
      >
        <mat-card-header>
          <mat-card-title>
            Teams
            <mat-icon
              class="info-icon"
              matTooltip="Teams participating in this game"
              >visibility</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <div class="read-only-list">
            <div
              *ngFor="let team of teams; let i = index"
              class="read-only-item"
            >
              <div class="read-only-label">Team {{ i + 1 }}</div>
              <div class="read-only-value">{{ team }}</div>
            </div>
            <div *ngIf="teams.length === 0" class="read-only-empty-message">
              No teams added yet
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Admin editable game settings card -->
      <mat-card
        class="admin-setup other-settings-card"
        *ngIf="isAdmin && !loading && !isGeneratingQuestions"
      >
        <mat-card-header>
          <mat-card-title>
            Game Settings
            <mat-icon
              class="info-icon"
              matTooltip="Configure game parameters and options"
              >settings</mat-icon
            >
          </mat-card-title>
        </mat-card-header>
        <mat-card-content
          class="category-input-list vertical-list-mat-card-content"
        >
          <!-- <mat-form-field class="mat-form-field">
          <mat-label>Number of Categories</mat-label>
          <input
            matInput
            type="number"
            [(ngModel)]="numCategories"
            min="1"
            max="10"
            placeholder="Enter a number between 1 and 10"
          />
        </mat-form-field> -->

          <mat-form-field class="mat-form-field">
            <mat-label>Number of Questions</mat-label>
            <input
              matInput
              type="number"
              [(ngModel)]="numQuestions"
              (change)="onNumQuestionsChange()"
              min="1"
              max="10"
              placeholder="Enter a number between 1 and 10"
            />
          </mat-form-field>

          <mat-form-field class="mat-form-field">
            <mat-label>LLM Agent</mat-label>
            <mat-select
              [(ngModel)]="llmAgent"
              (selectionChange)="onLlmAgentChange()"
            >
              <mat-option *ngFor="let agent of llmAgents" [value]="agent">
                {{ agent.modelName }} ({{ agent.provider }})
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field *ngIf="!isGamemasterMode" class="mat-form-field">
            <mat-label>Game mode</mat-label>
            <mat-select
              [(ngModel)]="gameMode"
              (selectionChange)="onGameModeChange()"
            >
              <mat-option value="BUZZER">
                Buzzer
              </mat-option>
              <mat-option value="TURN_BASED">
                Turn Based
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="mat-form-field">
            <mat-label>Question Style</mat-label>
            <mat-select
              [(ngModel)]="generationStrategy"
              (selectionChange)="onGenerationStrategyChange()"
            >
              <mat-option *ngFor="let strat of generationStrategies" [value]="strat.value">
                {{ strat.viewValue }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </mat-card-content>
      </mat-card>

      <!-- Non-admin read-only game settings card -->
      <mat-card
        class="read-only-card other-settings-card"
        *ngIf="!isAdmin && !loading && !isGeneratingQuestions"
      >
        <mat-card-header>
          <mat-card-title>
            Game Settings
            <mat-icon class="info-icon" matTooltip="Current game configuration"
              >visibility</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <div class="read-only-list">
            <div class="read-only-item">
              <div class="read-only-label">Number of Questions</div>
              <div class="read-only-value">{{ numQuestions }}</div>
            </div>
            <div class="read-only-item">
              <div class="read-only-label">LLM Agent</div>
              <div class="read-only-value">
                {{ llmAgent.modelName }} ({{ llmAgent.provider }})
              </div>
            </div>
            <div class="read-only-item" *ngIf="!isGamemasterMode">
              <div class="read-only-label">Game Mode</div>
              <div class="read-only-value">
                {{ gameMode === "BUZZER" ? "Buzzer" : "Turn Based" }}
              </div>
            </div>
            <div class="read-only-item">
              <div class="read-only-label">Question Style</div>
              <div class="read-only-value">
                {{ getStrategyViewValue(generationStrategy) }}
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Admin editable timer settings card for multiplayer mode -->
      <mat-card
        class="admin-setup timer-settings-card"
        *ngIf="
          isAdmin &&
          !loading &&
          !isGeneratingQuestions &&
          !isGamemasterMode &&
          showTimerSettings
        "
      >
        <mat-card-header>
          <mat-card-title
            >Timer Settings
            <mat-icon
              class="info-icon"
              matTooltip="Configure time limits for game actions"
              >timer</mat-icon
            ></mat-card-title
          >
          <button
            mat-icon-button
            class="reset-button"
            (click)="resetTimerSettings()"
          >
            <mat-icon>refresh</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content class="timer-settings-content">
          <div class="timer-setting" *ngIf="gameMode === 'BUZZER'">
            <div class="timer-label" [ngClass]="getTimerLabelClass('buzzIn')">
              Buzz-In Timer: {{ buzzInTimerDuration }} seconds
              <mat-icon
                class="info-icon"
                matTooltip="Time allowed for players to buzz in after a clue is revealed. Shorter times create more urgency."
                >info</mat-icon
              >
            </div>
            <mat-slider
              min="5"
              max="120"
              step="5"
              discrete
              [displayWith]="formatSliderLabel"
              (change)="onTimerChange('buzzIn')"
              [color]="getSliderColor(buzzInTimerDuration, 5, 120)"
            >
              <input matSliderThumb [(ngModel)]="buzzInTimerDuration" />
            </mat-slider>
          </div>

          <div class="timer-setting">
            <div class="timer-label" [ngClass]="getTimerLabelClass('answer')">
              Answer Timer: {{ answerDuration }} seconds
              <mat-icon
                class="info-icon"
                matTooltip="Time allowed for a player to provide an answer after buzzing in. Shorter times increase difficulty."
                >info</mat-icon
              >
            </div>
            <mat-slider
              min="5"
              max="120"
              step="5"
              discrete
              [displayWith]="formatSliderLabel"
              (change)="onTimerChange('answer')"
              [color]="getSliderColor(answerDuration, 5, 120)"
            >
              <input matSliderThumb [(ngModel)]="answerDuration" />
            </mat-slider>
          </div>

          <div class="timer-setting">
            <div
              class="timer-label"
              [ngClass]="getTimerLabelClass('showAnswer')"
            >
              Show Answer Duration: {{ showAnswerDuration }} seconds
              <mat-icon
                class="info-icon"
                matTooltip="Time to display the answer before moving to the next clue. Longer times allow players to read and understand the answer."
                >info</mat-icon
              >
            </div>
            <mat-slider
              min="5"
              max="30"
              step="5"
              discrete
              [displayWith]="formatSliderLabel"
              (change)="onTimerChange('showAnswer')"
              [color]="getSliderColor(showAnswerDuration, 5, 30)"
            >
              <input matSliderThumb [(ngModel)]="showAnswerDuration" />
            </mat-slider>
          </div>

          <div class="timer-setting">
            <div
              class="timer-label"
              [ngClass]="getTimerLabelClass('initialThinking')"
            >
              Initial Thinking Time: {{ initialThinkingDuration }} seconds
              <mat-icon
                class="info-icon"
                matTooltip="{{
                  gameMode === 'BUZZER'
                    ? 'Additional time given to the first player who buzzes in.'
                    : 'Additional time given to the first player to answer in turn-based mode.'
                }} Set to 0 for equal time for all players."
                >info</mat-icon
              >
            </div>
            <mat-slider
              min="0"
              max="60"
              step="5"
              discrete
              [displayWith]="formatSliderLabel"
              (change)="onTimerChange('initialThinking')"
              [color]="getSliderColor(initialThinkingDuration, 0, 60)"
            >
              <input matSliderThumb [(ngModel)]="initialThinkingDuration" />
            </mat-slider>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Admin editable timer settings card for gamemaster mode -->
      <mat-card
        class="admin-setup timer-settings-card"
        *ngIf="
          isAdmin &&
          !loading &&
          !isGeneratingQuestions &&
          isGamemasterMode &&
          showTimerSettings
        "
      >
        <mat-card-header>
          <mat-card-title
            >Timer Settings
            <mat-icon
              class="info-icon"
              matTooltip="Configure time limits for game actions"
              >timer</mat-icon
            ></mat-card-title
          >
          <button
            mat-icon-button
            class="reset-button"
            (click)="resetTimerSettings()"
          >
            <mat-icon>refresh</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content class="timer-settings-content">
          <div class="timer-setting">
            <div
              class="timer-label"
              [ngClass]="getTimerLabelClass('showAnswer')"
            >
              Show Answer Duration: {{ showAnswerDuration }} seconds
              <mat-icon
                class="info-icon"
                matTooltip="Time to display the answer after assigning points. Longer times allow players to read and understand the answer."
                >info</mat-icon
              >
            </div>
            <mat-slider
              min="5"
              max="30"
              step="5"
              discrete
              [displayWith]="formatSliderLabel"
              (change)="onTimerChange('showAnswer')"
              [color]="getSliderColor(showAnswerDuration, 5, 30)"
            >
              <input matSliderThumb [(ngModel)]="showAnswerDuration" />
            </mat-slider>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Non-admin read-only timer settings card for gamemaster mode -->
      <mat-card
        class="read-only-card timer-settings-card"
        *ngIf="
          !isAdmin &&
          !loading &&
          !isGeneratingQuestions &&
          isGamemasterMode &&
          showTimerSettings
        "
      >
        <mat-card-header>
          <mat-card-title
            >Timer Settings
            <mat-icon class="info-icon" matTooltip="Time settings for the game"
              >timer</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <div class="read-only-list">
            <div class="read-only-item">
              <div class="read-only-label">Show Answer Duration</div>
              <div class="read-only-value">
                {{ showAnswerDuration }} seconds
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Non-admin read-only timer settings card for multiplayer mode -->
      <mat-card
        class="read-only-card timer-settings-card"
        *ngIf="
          !isAdmin &&
          !loading &&
          !isGeneratingQuestions &&
          !isGamemasterMode &&
          showTimerSettings
        "
      >
        <mat-card-header>
          <mat-card-title
            >Timer Settings
            <mat-icon
              class="info-icon"
              matTooltip="Current time limits for game actions"
              >visibility</mat-icon
            ></mat-card-title
          >
        </mat-card-header>
        <mat-card-content class="vertical-list-mat-card-content">
          <div class="read-only-list">
            <div class="read-only-item" *ngIf="gameMode === 'BUZZER'">
              <div class="read-only-label">Buzz-In Timer</div>
              <div class="read-only-value">
                {{ buzzInTimerDuration }} seconds
              </div>
              <mat-icon
                class="info-icon"
                matTooltip="Time allowed for players to buzz in after a clue is revealed. Shorter times create more urgency."
                >info</mat-icon
              >
            </div>
            <div class="read-only-item">
              <div class="read-only-label">Answer Timer</div>
              <div class="read-only-value">{{ answerDuration }} seconds</div>
              <mat-icon
                class="info-icon"
                matTooltip="Time allowed for a player to provide an answer after buzzing in. Shorter times increase difficulty."
                >info</mat-icon
              >
            </div>
            <div class="read-only-item">
              <div class="read-only-label">Show Answer Duration</div>
              <div class="read-only-value">
                {{ showAnswerDuration }} seconds
              </div>
              <mat-icon
                class="info-icon"
                matTooltip="Time to display the answer before moving to the next clue. Longer times allow players to read and understand the answer."
                >info</mat-icon
              >
            </div>
            <div class="read-only-item">
              <div class="read-only-label">Initial Thinking Time</div>
              <div class="read-only-value">
                {{ initialThinkingDuration }} seconds
              </div>
              <mat-icon
                class="info-icon"
                matTooltip="{{
                  gameMode === 'BUZZER'
                    ? 'Additional time given to the first player who buzzes in.'
                    : 'Additional time given to the first player to answer in turn-based mode.'
                }} Set to 0 for equal time for all players."
                >info</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>

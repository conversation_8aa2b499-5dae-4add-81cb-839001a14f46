import { ComponentFixture, TestBed } from '@angular/core/testing';
import { WaitingRoomComponent } from './waiting-room.component';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { GameService } from '../services/game.service';
import { LlmProgressService } from '../services/llm-progress.service';
import { GameMode } from '../../../functions/src/resources';

describe('WaitingRoomComponent', () => {
  let component: WaitingRoomComponent;
  let fixture: ComponentFixture<WaitingRoomComponent>;
  let gameServiceSpy: jasmine.SpyObj<GameService>;
  let llmProgressServiceSpy: jasmine.SpyObj<LlmProgressService>;

  beforeEach(async () => {
    gameServiceSpy = jasmine.createSpyObj('GameService', ['call']);
    llmProgressServiceSpy = jasmine.createSpyObj('LlmProgressService', [
      'progress$',
      'resetProgress'
    ]);
    llmProgressServiceSpy.progress$ = of({
      progress: 0,
      message: '',
      category: ''
    });

    await TestBed.configureTestingModule({
      imports: [WaitingRoomComponent, NoopAnimationsModule],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: {
                get: () => 'test-room-id'
              }
            },
            queryParams: of({})
          }
        },
        { provide: GameService, useValue: gameServiceSpy },
        { provide: LlmProgressService, useValue: llmProgressServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WaitingRoomComponent);
    component = fixture.componentInstance;
    component.room = {
      roomId: 'test-room',
      host: 'host-id',
      mode: GameMode.BUZZER,
      playerIds: [],
      playerInfos: [],
      gameState: {
        gameProgress: {
          type: 'Waiting'
        }
      }
    } as any;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import { Component, ElementRef, ViewChild } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MediaControlService } from '../services/media-control.service';
import { MediaRegisterDirective } from './media-register.directive';

@Component({
  template: `<video appMediaRegister="test-video"></video>`,
  standalone: true,
  imports: [MediaRegisterDirective]
})
class TestComponent {
  @ViewChild(MediaRegisterDirective) directive!: MediaRegisterDirective;
}

describe('MediaRegisterDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let mediaControlServiceSpy: jasmine.SpyObj<MediaControlService>;
  let videoElement: HTMLVideoElement;

  beforeEach(async () => {
    mediaControlServiceSpy = jasmine.createSpyObj('MediaControlService', [
      'registerMediaElement',
      'unregisterMediaElement'
    ]);

    await TestBed.configureTestingModule({
      imports: [TestComponent, MediaRegisterDirective],
      providers: [
        { provide: MediaControlService, useValue: mediaControlServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    videoElement = fixture.nativeElement.querySelector('video');
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    expect(component.directive).toBeTruthy();
  });

  it('should register the media element on init', () => {
    expect(
      mediaControlServiceSpy.registerMediaElement
    ).toHaveBeenCalledWith(videoElement, 'test-video');
  });

  it('should unregister the media element on destroy', () => {
    mediaControlServiceSpy.registerMediaElement.and.returnValue('test-id');
    component.directive.ngOnInit();
    fixture.destroy();
    expect(
      mediaControlServiceSpy.unregisterMediaElement
    ).toHaveBeenCalledWith('test-id');
  });
});

import { Component, ViewChild } from '@angular/core';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { PlyrService } from '../services/plyr.service';
import { PlyrRegisterDirective } from './plyr-register.directive';

@Component({
  template: `<div appPlyrRegister>
    <video src="test.mp4" data-plyr-provider="html5"></video>
    <iframe
      src="https://www.youtube.com/embed/test"
      data-plyr-provider="youtube"
    ></iframe>
  </div>`,
  standalone: true,
  imports: [PlyrRegisterDirective],
})
class TestComponent {
  @ViewChild(PlyrRegisterDirective) directive!: PlyrRegisterDirective;
}

describe('PlyrRegisterDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let plyrServiceSpy: jasmine.SpyObj<PlyrService>;

  beforeEach(async () => {
    plyrServiceSpy = jasmine.createSpyObj('PlyrService', [
      'createPlayer',
      'destroy',
    ]);

    await TestBed.configureTestingModule({
      imports: [TestComponent, PlyrRegisterDirective],
      providers: [{ provide: PlyrService, useValue: plyrServiceSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
  });

  it('should create an instance', () => {
    fixture.detectChanges();
    expect(component.directive).toBeTruthy();
  });

  it('should scan and initialize media elements', fakeAsync(() => {
    fixture.detectChanges();
    tick(100);
    expect(plyrServiceSpy.createPlayer).toHaveBeenCalledTimes(2);
  }));

  it('should handle dynamically added media elements', fakeAsync(() => {
    fixture.detectChanges();
    tick(100);
    expect(plyrServiceSpy.createPlayer).toHaveBeenCalledTimes(2); // Initial scan

    const newVideo = document.createElement('video');
    newVideo.src = 'new-test.mp4';
    newVideo.setAttribute('data-plyr-provider', 'html5');
    fixture.nativeElement.querySelector('div').appendChild(newVideo);
    component.directive['scanAndInitializeMedia']();
    tick(100);

    expect(plyrServiceSpy.createPlayer).toHaveBeenCalledTimes(3);
  }));

  it('should destroy players on ngOnDestroy', fakeAsync(() => {
    plyrServiceSpy.createPlayer.and.callFake((el, qid, opts) => {
      if (el.tagName === 'VIDEO') return Promise.resolve('player1');
      if (el.tagName === 'DIV') return Promise.resolve('player2');
      return Promise.resolve('unknown-player');
    });

    fixture.detectChanges();
    tick(100);

    fixture.destroy();

    expect(plyrServiceSpy.destroy).toHaveBeenCalledTimes(2);
    expect(plyrServiceSpy.destroy).toHaveBeenCalledWith('player1');
    expect(plyrServiceSpy.destroy).toHaveBeenCalledWith('player2');
  }));
});
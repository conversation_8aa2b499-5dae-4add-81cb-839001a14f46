import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { CategoryWizardComponent } from './category-wizard.component';

describe('CategoryWizardComponent', () => {
  let component: CategoryWizardComponent;
  let fixture: ComponentFixture<CategoryWizardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CategoryWizardComponent, NoopAnimationsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CategoryWizardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import {
  AfterViewInit,
  Component,
  EventEmitter,
  inject,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import {
  MatCheckboxChange,
  MatCheckboxModule
} from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { DynamicListInputComponent } from '../dynamic-list-input/dynamic-list-input.component';
import { MatCardModule } from '@angular/material/card';
import { GameService } from '../services/game.service';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import {
  GenerateSubCategoriesResponse,
  GenerateSubCategoriesRequest,
  SubCategory
} from '../../../functions/src/services';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-category-wizard',
  templateUrl: './category-wizard.component.html',
  styleUrls: ['./category-wizard.component.css'],
  imports: [
    MatExpansionModule,
    CommonModule,
    MatCardModule,
    MatCheckboxModule,
    MatFormFieldModule,
    FormsModule,
    MatProgressBarModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    DynamicListInputComponent
  ],
  standalone: true
})
export class CategoryWizardComponent implements AfterViewInit {
  broadAreas = {
    Entertainment: {
      Movies: {},
      'TV Shows': {},
      Music: {},
      Books: {},
      Authors: <AUTHORS>
      'Video Games': {},
      'Board Games': {},
      Theatre: {},
      Celebrities: {},
      'Pop Culture': {},
      'Comics & Graphic Novels': {},
      'Internet Culture': {}
    },
    'Science & Technology': {
      'Space Exploration': {},
      Astronomy: {},
      Computers: {},
      'Artificial Intelligence': {},
      Robotics: {},
      Engineering: {},
      Health: {},
      Medicine: {},
      'Inventions & Inventors': {},
      'Scientific Discoveries': {}
    },
    'History & Culture': {
      'World History': {},
      'US History': {},
      'Ancient History': {},
      'Military History': {},
      Art: {},
      Design: {},
      Mythology: {},
      Religion: {},
      Philosophy: {},
      Languages: {},
      Archaeology: {},
      'Ancient Civilizations': {}
    },
    'Sports & Recreation': {
      'Team Sports': {},
      'Individual Sports': {},
      'Olympic Sports': {},
      'Outdoor Activities': {},
      Fitness: {},
      Wellness: {},
      Hobbies: {},
      'Board Games': {},
      'Card Games': {},
      'Puzzles & Brain Teasers': {}
    },
    'Society & Life': {
      'Current Events': {},
      Politics: {},
      'Social Issues': {},
      'Food & Drink': {},
      Travel: {},
      Tourism: {},
      Fashion: {},
      Lifestyle: {},
      Education: {},
      'Law & Crime': {}
    },
    'Geography & Places': {
      Countries: {},
      Cities: {},
      Landmarks: {},
      Continents: {},
      Oceans: {},
      Mountains: {},
      Rivers: {},
      Geography: {},
      'Flags & Symbols': {}
    },
    Transportation: {
      Aviation: {},
      Automotive: {},
      Trains: {},
      Ships: {},
      'Space Travel': {}
    },
    'Business & Economics': {
      Companies: {},
      Brands: {},
      Finance: {},
      Economics: {},
      'Marketing & Advertising': {},
      Industries: {}
    },
    'The Natural World': {
      Animals: {},
      Plants: {},
      Ecology: {},
      Geology: {},
      Weather: {}
    },
    'Language & Linguistics': {
      Etymology: {},
      'Languages of the World': {},
      'Famous Quotes': {}
    }
  };
  generatedSubCategories?: GenerateSubCategoriesResponse;
  isLoading: boolean = false;
  gameService = inject(GameService);
  @ViewChild('dynamicList')
  categoryList!: DynamicListInputComponent;
  @Output() emitSubCategories = new EventEmitter<SubCategory>();

  allBroadAreas: Array<string> = [];

  constructor() {
    this.allBroadAreas = this.getAllKeys(this.broadAreas, []);
  }
  ngAfterViewInit() {
    setTimeout(() => this.resetCategories());
  }

  private getAllKeys(obj: any, keys: Array<string> = []) {
    for (const key in obj) {
      keys.push(key as string);
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.getAllKeys(obj[key], keys);
      }
    }
    return keys;
  }
  resetCategories() {
    console.log('Resetting categories');
    this.categoryList.clearElements();
    this.categoryList.addElement('General Knowledge');
    // Pick an arbitrary broad Area/subcategory randomly.
    const randomBroadAreaKey: string = this.allBroadAreas[
      Math.floor(Math.random() * this.allBroadAreas.length)
    ];
    this.categoryList.addElement(randomBroadAreaKey);
  }
  async generateSubCategories() {
    this.isLoading = true;
    const req: GenerateSubCategoriesRequest = {
      categories: this.categoryList.getList()
    };
    // Call your LLM API here (replace with your actual API endpoint)
    this.generatedSubCategories = (
      await FirebaseFunctions.callByName({
        name: 'generateSubCategories',
        data: req
      })
    ).data as GenerateSubCategoriesResponse;
    this.isLoading = false;
  }

  addCategory(subcategory: string, event: MouseEvent) {
    event.stopPropagation();
    this.categoryList.addElement(subcategory);
  }

  addSubCategoryToGame(subCategory: SubCategory) {
    this.emitSubCategories.emit(subCategory);
  }
}

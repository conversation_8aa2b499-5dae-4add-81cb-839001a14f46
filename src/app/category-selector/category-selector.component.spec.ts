import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CategorySelectorComponent } from './category-selector.component';
import { GameService } from '../services/game.service';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SubCategory } from '../../../functions/src/services';

describe('CategorySelectorComponent', () => {
  let component: CategorySelectorComponent;
  let fixture: ComponentFixture<CategorySelectorComponent>;
  let gameServiceSpy: jasmine.SpyObj<GameService>;

  beforeEach(async () => {
    gameServiceSpy = jasmine.createSpyObj('GameService', ['call']);

    await TestBed.configureTestingModule({
      imports: [CategorySelectorComponent, NoopAnimationsModule],
      providers: [{ provide: GameService, useValue: gameServiceSpy }]
    }).compileComponents();

    fixture = TestBed.createComponent(CategorySelectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should spawn initial categories', () => {
    component.spawnInitialCategories();
    expect(component.spawnedCategories.length).toBe(8);
  });

  it('should add a category', () => {
    spyOn(component.emitSubCategories, 'emit');
    const category = 'Test Category';
    component.addCategory(category, 0);
    expect(component.selectedCategories).toContain(category);
    const expectedSubCategory: SubCategory = {
      subCategory: category,
      description: `Category: ${category}`
    };
    expect(component.emitSubCategories.emit).toHaveBeenCalledWith(
      expectedSubCategory
    );
  });

  it('should remove a category', () => {
    const category = 'Test Category';
    component.selectedCategories = [category];
    component.removeCategory(category);
    expect(component.selectedCategories).not.toContain(category);
  });

  it('should refresh all categories', () => {
    const oldCategories = [...component.spawnedCategories];
    component.refreshAllCategories();
    expect(component.spawnedCategories).not.toEqual(oldCategories);
    expect(component.spawnedCategories.length).toBe(8);
  });
});

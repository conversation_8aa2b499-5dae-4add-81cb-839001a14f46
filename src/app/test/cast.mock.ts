// Create a mock for the cast object
export const cast = {
  framework: {
    CastContext: {
      getInstance: () => ({
        setOptions: () => {},
        getCastState: () => 'NOT_CONNECTED',
        addEventListener: () => {},
        removeEventListener: () => {},
        requestSession: () => Promise.resolve()
      })
    },
    CastState: {
      NOT_CONNECTED: 'NOT_CONNECTED',
      CONNECTED: 'CONNECTED',
      CONNECTING: 'CONNECTING',
      NO_DEVICES_AVAILABLE: 'NO_DEVICES_AVAILABLE'
    },
    SessionState: {
      SESSION_STARTED: 'SESSION_STARTED',
      SESSION_RESUMED: 'SESSION_RESUMED',
      SESSION_ENDED: 'SESSION_ENDED'
    },
    RemotePlayer: function () {},
    RemotePlayerController: function () {}
  }
};

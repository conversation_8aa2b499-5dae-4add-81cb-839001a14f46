import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { environment } from '../../environments/environment';

export const initializeTestBed = () => {
  const firebaseApp = initializeApp(environment.firebase);
  connectAuthEmulator(getAuth(), 'http://localhost:9099');
  connectFirestoreEmulator(getFirestore(), 'localhost', 8080);
  connectFunctionsEmulator(getFunctions(), 'localhost', 5001);
};

import { inject, Injectable } from '@angular/core';
import { Room, Round, Message } from '@functions/resources';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import { LlmProgressService } from './llm-progress.service';
import { GenerationProgressService } from './generation-progress.service';
import { BehaviorSubject, Observable, from, map, of } from 'rxjs';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import { Client, Room as ColyseusRoom } from 'colyseus.js';
import { AuthService } from './auth.service';
import { Room as ColyseusRoomState } from '@colyseus-server/room_schema';
import { adaptColyseusRoomToLegacy } from '@colyseus-server/room_adapter';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private matSnackBar = inject(MatSnackBar);
  private llmProgressService = inject(LlmProgressService);
  private generationProgressService = inject(GenerationProgressService);
  private authService = inject(AuthService);

  private colyseusClient?: Client;
  private rawColyseusRoom: ColyseusRoom<ColyseusRoomState> | null = null;
  private adaptedRoomState = new BehaviorSubject<Room | null>(null);
  private streamedGenerationResponse = new BehaviorSubject<string>('');
  public streamedGenerationResponse$ = this.streamedGenerationResponse.asObservable();
  private generationCompleted = new BehaviorSubject<boolean>(false);
  public generationCompleted$ = this.generationCompleted.asObservable();
  private chatMessages = new BehaviorSubject<Message[]>([]);
  public chatMessages$ = this.chatMessages.asObservable();

  constructor() {
    if (environment.useColyseus) {
      this.colyseusClient = new Client(environment.colyseusUrl);
    }
  }

  // --- Room Operations ---
  async call(functionName: string, data: any = {}): Promise<any> {
    console.log(`Calling function '${functionName}' with data:`, data);
    if (environment.useColyseus) {
      if (functionName === 'createRoom' || functionName === 'joinRoom') {
        return this.joinOrCreateColyseusRoom(data.roomId, data);
      }
      if (!this.rawColyseusRoom) {
        console.error(
          `Cannot call '${functionName}': Not connected to a room.`
        );
        return Promise.reject('Not connected to a room.');
      }
      this.rawColyseusRoom.send(functionName, data);
      console.log(`Sent message '${functionName}' to room.`);
      return Promise.resolve();
    } else {
      // Legacy Firebase Functions implementation
      if (functionName === 'startRoom') {
        await this.generationProgressService.startGenerationPhase(data.roomId);
        this.simulateProgressUpdates(data);
      }

      try {
        const result = await FirebaseFunctions.callByName({
          name: functionName,
          data
        });

        if (functionName === 'startRoom') {
          this.llmProgressService.resetProgress();
        }
        console.log(
          `Function '${functionName}' called successfully. Result:`,
          result.data
        );
        return result.data as Room;
      } catch (error) {
        console.error(`Error calling function ${functionName}:`, error);

        if (functionName === 'startRoom') {
          this.llmProgressService.resetProgress();
        }

        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        this.matSnackBar.open('Error: ' + errorMessage, 'Dismiss', {
          duration: 3000
        });

        throw new Error(`Failed to call ${functionName}`);
      }
    }
  }

  async joinOrCreateColyseusRoom(
    roomId: string,
    options: any = {}
  ): Promise<Room> {
    console.log(`Attempting to join or create Colyseus room '${roomId}'...`);
    const user = this.authService.getUser();
    let token: string | null = null;
    if (user) {
      token = await this.authService.getIdToken();
    }
    try {
      const seatReservation = await this.colyseusClient?.http.post(
        '/getOrCreate/jeopardy_room',
        {
          body: {
            roomId,
            token,
            userName: localStorage.getItem('username'),
            ...options
          }
        }
      );
      const room = (await this.colyseusClient?.consumeSeatReservation(
        seatReservation!.data
      )!) as any;
      console.log(
        `Successfully joined or created room '${roomId}'. Setting up room...`
      );
      this.setupColyseusRoom(room);
      const res = adaptColyseusRoomToLegacy(room);
      return res;
    } catch (error) {
      console.error(`Failed to join or create room '${roomId}':`, error);
      throw error;
    }
  }

  private setupColyseusRoom(room: ColyseusRoom<ColyseusRoomState>) {
    console.log(`Setting up listeners for room '${room.roomId}'.`);
    this.rawColyseusRoom = room;

    // Initial state adaptation
    this.adaptedRoomState.next(adaptColyseusRoomToLegacy(room));

    // Subscribe to future state changes
    room.onStateChange(() => {
      console.log(
        `State change detected in room '${room.roomId}'. Adapting and updating local state.`
      );
      console.log(JSON.stringify(room.state, null, 2));
      this.adaptedRoomState.next(adaptColyseusRoomToLegacy(room));
    });

    room.onMessage('*', (type, message) => {
      // console.log(
      //   `Received message of type '${type}' from room '${room.roomId}':`,
      //   message
      // );
      if (type === 'generationChunk') {
        this.streamedGenerationResponse.next(
          this.streamedGenerationResponse.value + message
        );
      }
      if (type === 'generationComplete') {
        this.generationCompleted.next(true);
      }
      if (type === 'chat_message') {
        const currentMessages = this.chatMessages.value;
        const newMessage: Message = {
          id: (currentMessages.length + 1).toString(),
          playerId: message.senderId,
          text: message.content,
          timestamp: message.timestamp,
          roomId: room.roomId
        };
        this.chatMessages.next([...currentMessages, newMessage]);
      }
    });

    room.onError((code, message) => {
      console.error(
        `An error occurred in room '${room.roomId}' (code ${code}):`,
        message
      );
    });

    room.onLeave(code => {
      console.log(`Left room '${room.roomId}' (code ${code}).`);
    });
  }

  leaveRoom() {
    if (this.rawColyseusRoom) {
      console.log(`Leaving room '${this.rawColyseusRoom.roomId}'...`);
      this.rawColyseusRoom.leave();
      this.rawColyseusRoom = null;
      this.adaptedRoomState.next(null);
      console.log('Room left and local state cleared.');
    }
  }

  /**
   * Simulate progress updates for LLM generation
   * In a real implementation, this would be replaced with actual streaming updates from the LLM
   */
  private simulateProgressUpdates(data: any): void {
    const { roomId, categoryTitles, numQuestions } = data;
    const totalCategories = categoryTitles.length;
    const totalQuestionsPerCategory = numQuestions;

    // Reset progress
    this.llmProgressService.resetProgress();

    // Initial progress update
    const initialUpdate = {
      progress: 0,
      message: 'Starting question generation...'
    };

    this.llmProgressService.updateProgress(initialUpdate);
    this.generationProgressService.updateGenerationProgress(
      roomId,
      initialUpdate
    );

    // Simulate progress for each category and question
    let currentCategory = 0;
    let currentQuestion = 0;

    const updateInterval = setInterval(async () => {
      // Generate a progress message
      const message = this.llmProgressService.generateProgressMessage(
        categoryTitles[currentCategory],
        currentQuestion
      );

      // Calculate progress percentage
      const progress = this.llmProgressService.calculateProgress(
        totalCategories,
        totalQuestionsPerCategory,
        currentCategory,
        currentQuestion,
        Math.random() * 100 // Simulate progress within the current question
      );

      // Create the progress update
      const progressUpdate = {
        progress: Math.round(progress),
        message,
        category: categoryTitles[currentCategory],
        questionIndex: currentQuestion
      };

      // Update progress locally and in Firebase
      this.llmProgressService.updateProgress(progressUpdate);
      await this.generationProgressService.updateGenerationProgress(
        roomId,
        progressUpdate
      );

      // Move to the next question or category
      currentQuestion++;
      if (currentQuestion >= totalQuestionsPerCategory) {
        currentQuestion = 0;
        currentCategory++;
      }

      // Stop when all categories are done
      if (currentCategory >= totalCategories) {
        clearInterval(updateInterval);

        // Final update
        const finalUpdate = {
          progress: 100,
          message: 'Finalizing game setup...'
        };

        this.llmProgressService.updateProgress(finalUpdate);
        await this.generationProgressService.updateGenerationProgress(
          roomId,
          finalUpdate
        );
      }
    }, 1500); // Update every 1.5 seconds
  }

  /**
   * Subscribe to room updates using real-time listener
   */
  subscribeToRoom(
    roomId: string,
    joinOptions: any = {}
  ): Observable<Room | null> {
    console.log(`Subscribing to room '${roomId}'...`);

    if (environment.useColyseus) {
      // If we're already connected to the correct room, return the existing observable.
      if (this.rawColyseusRoom && this.rawColyseusRoom.roomId === roomId) {
        console.log(
          `Already connected to room '${roomId}'. Reusing connection.`
        );
        return this.adaptedRoomState.asObservable();
      }

      // If we are connected to a different room, leave it first.
      if (this.rawColyseusRoom) {
        console.log(
          `Leaving previous room '${this.rawColyseusRoom.roomId}' before joining '${roomId}'.`
        );
        this.leaveRoom();
      }

      // No active connection, so we need to join.
      console.log(
        `No active room connection for '${roomId}'. Attempting to join...`
      );
      this.joinOrCreateColyseusRoom(roomId, joinOptions).catch(error => {
        console.error(`Failed to join room in subscribeToRoom:`, error);
        this.adaptedRoomState.next(null); // Notify subscribers of the failure
      });

      return this.adaptedRoomState.asObservable();
    } else {
      // Legacy Firebase implementation remains the same.
      console.log(`Using Firebase listener for room '${roomId}'.`);
      return new Observable(observer => {
        let callbackId: string | undefined;

        const setupListener = async () => {
          try {
            const roomRef = `rooms/${roomId}`;
            callbackId = await FirebaseFirestore.addDocumentSnapshotListener(
              { reference: roomRef },
              (event, error) => {
                if (error) {
                  console.error(
                    `Error in Firebase listener for room '${roomId}':`,
                    error
                  );
                  observer.error(error);
                } else if (event?.snapshot.data == null) {
                  console.log(`No data for room '${roomId}'.`);
                  observer.next(null);
                } else {
                  console.log(`Received update for room '${roomId}'.`);
                  observer.next(event.snapshot.data as Room);
                }
              }
            );
          } catch (error) {
            console.error(
              `Failed to set up Firebase listener for room '${roomId}':`,
              error
            );
            observer.error(error);
          }
        };

        setupListener();

        return () => {
          if (callbackId) {
            console.log(`Unsubscribing from room '${roomId}'.`);
            FirebaseFirestore.removeSnapshotListener({
              callbackId: callbackId
            });
          }
        };
      });
    }
  }

  getRoom(): ColyseusRoom<ColyseusRoomState> | null {
    return this.rawColyseusRoom;
  }

  /**
   * Get round data for a specific room and round
   */
  async getRoundData(roomId: string, roundIdx: number): Promise<Round | null> {
    if (environment.useColyseus) {
      // In Colyseus, the round data is adapted once and stored in the adaptedRoomState.
      const room = this.adaptedRoomState.value;
      if (
        room &&
        room.roomData &&
        room.roomData.rounds &&
        room.roomData.rounds[roundIdx]
      ) {
        return room.roomData.rounds[roundIdx];
      }
      return null;
    } else {
      try {
        const result = await FirebaseFunctions.callByName({
          name: 'getRoundData',
          data: { roomId, roundIdx }
        });
        return result.data as Round;
      } catch (error) {
        console.error('Error getting round data:', error);
        return null;
      }
    }
  }
}

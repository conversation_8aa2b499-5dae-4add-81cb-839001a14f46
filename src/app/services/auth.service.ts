import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, from, Observable, of, switchMap, tap } from 'rxjs';
import { GameService } from './game.service';
import {
  FirebaseAuthentication,
  User
} from '@capacitor-firebase/authentication';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  authBehaviorSubject = new BehaviorSubject<User | null>(null);
  user: User | null = null;
  authState = this.authBehaviorSubject.asObservable();

  constructor() {
    console.log('AuthService initialized.');
    FirebaseAuthentication.addListener('authStateChange', state => {
      if (state.user) {
        console.log(`Authentication state changed: User is now logged in as ${state.user.displayName} (${state.user.uid}).`);
      } else {
        console.log("Authentication state changed: User is now logged out.");
      }
      this.authBehaviorSubject.next(state.user);
      this.user = state.user;
    });
  }

  getUser() {
    return this.user;
  }

  // Google Sign-in
  signInWithGoogle(): Observable<User | null> {
    console.log("Attempting to sign in with Google...");
    return from(FirebaseAuthentication.signInWithGoogle()).pipe(
      tap(result => {
        if (result.user) {
          console.log(`Successfully signed in with Google as ${result.user.displayName} (${result.user.uid}).`);
        }
        this.authBehaviorSubject.next(result.user);
        this.user = result.user;
      }),
      switchMap(() => this.authState)
    );
  }

  // Anonymous Sign-in
  signInAnonymously(): Observable<User | null> {
    console.log("Attempting to sign in anonymously...");
    return from(FirebaseAuthentication.signInAnonymously()).pipe(
      tap(result => {
        if (result.user) {
          console.log(`Successfully signed in anonymously with UID: ${result.user.uid}.`);
        }
        this.authBehaviorSubject.next(result.user);
        this.user = result.user;
      }),
      switchMap(() => this.authState)
    );
  }

  // Sign out (works for all providers)
  logout(): Promise<void> {
    console.log("Attempting to sign out...");
    return FirebaseAuthentication.signOut().then(() => {
      console.log("Successfully signed out.");
    });
  }

  getAuthState(): Observable<User | null> {
    return this.authState!;
  }

  async getIdToken(): Promise<string | null> {
    console.log("Getting ID token...");
    const result = await FirebaseAuthentication.getIdToken();
    console.log("Successfully retrieved ID token.");
    return result.token;
  }
}

import { Injectable, inject } from '@angular/core';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from '../../environments/environment';
import { GameService } from './game.service';

@Injectable({
  providedIn: 'root'
})
export class GameEndService {
  private matSnackBar = inject(MatSnackBar);
  private gameService = inject(GameService);

  /**
   * End the game and show the winner's screen
   * @param roomId The ID of the room to end
   */
  async endGame(roomId: string): Promise<void> {
    try {
      if (environment.useColyseus) {
        await this.gameService.call('endGame', { roomId });
      } else {
        // Create a custom function to end the game
        await FirebaseFunctions.callByName({
          name: 'pingRoom',
          data: {
            roomId,
            playerId: 'system',
            endGame: true
          }
        });
      }
    } catch (error) {
      console.error('Error ending game:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.matSnackBar.open('Error ending game: ' + errorMessage, 'Dismiss', {
        duration: 3000
      });
      throw new Error('Failed to end game');
    }
  }

  /**
   * Revert from winner screen back to the game board
   * @param roomId The ID of the room to revert
   */
  async revertToGameBoard(roomId: string): Promise<void> {
    try {
      if (environment.useColyseus) {
        await this.gameService.call('returnToBoard', { roomId });
      } else {
        // Call a custom function to revert the game state
        await FirebaseFunctions.callByName({
          name: 'revertToGameState',
          data: {
            roomId,
            playerId: 'system'
          }
        });
      }

      this.matSnackBar.open('Returned to game board', 'Dismiss', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error reverting to game board:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.matSnackBar.open('Error: ' + errorMessage, 'Dismiss', {
        duration: 3000
      });
      throw new Error('Failed to revert to game board');
    }
  }
}

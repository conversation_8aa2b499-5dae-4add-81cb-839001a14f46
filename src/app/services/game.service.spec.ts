import { TestBed } from '@angular/core/testing';
import { GameService } from './game.service';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import {
  Room as ColyseusRoomState,
  RoomSettings as ColyseusRoomSettings,
  LlmAgent as ColyseusLlmAgent,
  GameMode as ColyseusGameMode,
  GenerationStrategy,
  FullRoomState as ColyseusFullRoomState,
  GeneratingQuestions as ColyseusGeneratingQuestions,
  RoomSummary as ColyseusRoomSummary,
  MemorableQuestion as ColyseusMemorableQuestion,
  AnswerExplanation as ColyseusAnswerExplanation,
  RoomData as ColyseusRoomData,
  RoundData as ColyseusRoundData,
  Category as ColyseusCategory,
  Clue as ColyseusClue,
  ArraySchema,
} from '@colyseus-server/room_schema';
import { strict as assert } from 'assert';

describe('GameService', () => {
  let service: GameService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MatSnackBarModule],
      providers: [GameService],
    });
    service = TestBed.inject(GameService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Data Adaptation Logic', () => {
    it('should correctly adapt RoomSettings', () => {
      const colyseusSettings = new ColyseusRoomSettings({
        gameMode: ColyseusGameMode.BUZZER,
        buzzInTimerDurationMillis: 15000,
        answerDurationMillis: 20000,
        showAnswerDurationMillis: 5000,
        llmAgent: new ColyseusLlmAgent({ provider: 'OpenAI', modelName: 'gpt-4' }),
        numQuestions: 7,
        generationStrategy: GenerationStrategy.SHORT_AND_SWEET,
      });

      const adapted = (service as any).adaptRoomSettings(colyseusSettings);

      assert.deepStrictEqual(adapted, {
        gameMode: 2, // LegacyGameMode.BUZZER
        buzzInTimerDurationMillis: 15000,
        answerDurationMillis: 20000,
        showAnswerDurationMillis: 5000,
        initialThinkingDurationMillis: 0,
        llmAgent: { provider: 'OpenAI', modelName: 'gpt-4' },
        numQuestions: 7,
        generationStrategy: 2, // GenerationStrategy.SHORT_AND_SWEET
      });
    });

    it('should correctly adapt GeneratingQuestions state', () => {
        const colyseusState = new ColyseusFullRoomState({
            progressType: 'GeneratingQuestions',
            generatingQuestions: new ColyseusGeneratingQuestions({
                progress: 50,
                message: 'Working on it...',
                category: 'Test Category',
                questionIndex: 2,
                startTime: 12345,
                streamedResponse: 'chunk',
                questionsGenerated: new ArraySchema<number>(1, 2, 0),
            }),
        });

        const adapted = (service as any).adaptGameProgress(colyseusState);

        assert.deepStrictEqual(adapted, {
            type: 'GeneratingQuestions',
            progress: 50,
            message: 'Working on it...',
            category: 'Test Category',
            questionIndex: 2,
            startTime: 12345,
            streamedResponse: 'chunk',
            questionsGenerated: [1, 2, 0],
        });
    });

    it('should correctly adapt RoomSummary state', () => {
        const colyseusState = new ColyseusFullRoomState({
            progressType: 'RoomSummary',
            roomSummary: new ColyseusRoomSummary({
                memorableQuestions: new ArraySchema<ColyseusMemorableQuestion>(
                    new ColyseusMemorableQuestion({ category: 'C1', question: 'Q1', answer: 'A1', value: 100, reason: 'R1' })
                )
            })
        });

        const adapted = (service as any).adaptGameProgress(colyseusState);

        assert.deepStrictEqual(adapted.type, 'RoomSummary');
        assert.deepStrictEqual(adapted.memorableQuestions, [
            { category: 'C1', question: 'Q1', answer: 'A1', value: 100, reason: 'R1' }
        ]);
    });

    it('should correctly adapt AnswerExplanation', () => {
        const colyseusExplanation = new ColyseusAnswerExplanation({
            isCorrect: true,
            explanation: 'That is correct!',
        });

        const adapted = (service as any).adaptAnswerExplanation(colyseusExplanation);

        assert.deepStrictEqual(adapted, {
            isCorrect: true,
            explanation: 'That is correct!',
        });
    });

    it('should correctly adapt RoomData and preserve clue order', () => {
        const colyseusRoomData = new ColyseusRoomData({
            rounds: new ArraySchema<ColyseusRoundData>(
                new ColyseusRoundData({
                    categories: new ArraySchema<ColyseusCategory>(
                        new ColyseusCategory({
                            categoryTitle: 'Test',
                            clues: new ArraySchema<ColyseusClue>(
                                new ColyseusClue({ value: 100, answer: 'A1' }),
                                new ColyseusClue({ value: 200, answer: 'A2' }),
                                new ColyseusClue({ value: 300, answer: 'A3' })
                            )
                        })
                    )
                })
            )
        });

        const adapted = (service as any).adaptRoomData(colyseusRoomData);
        const adaptedClues = adapted.rounds[0].categories[0].clues;

        assert.equal(adaptedClues.length, 3);
        assert.equal(adaptedClues[0].value, 100);
        assert.equal(adaptedClues[1].value, 200);
        assert.equal(adaptedClues[2].value, 300);
        assert.equal(adaptedClues[0].answer, 'A1');
    });
  });
});
import { TestBed } from '@angular/core/testing';
import { ChatService } from './chat.service';
import { AuthService } from './auth.service';

describe('ChatService', () => {
  let service: ChatService;
  let authServiceSpy: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getUser']);
    authServiceSpy.getUser.and.returnValue({ uid: 'test-user-id' } as any);

    TestBed.configureTestingModule({
      providers: [{ provide: AuthService, useValue: authServiceSpy }]
    });
    service = TestBed.inject(ChatService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

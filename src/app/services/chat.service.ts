import { Injectable, inject } from '@angular/core';
import { serverTimestamp } from '@angular/fire/firestore';
import { Observable, map } from 'rxjs';
import { Message } from '../../../functions/src/resources';
import { AuthService } from './auth.service';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import { environment } from '../../environments/environment';
import { GameService } from './game.service';
import { ChatMessage } from '@colyseus-server/room_schema';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private authService = inject(AuthService);
  private gameService = inject(GameService);
  private playerId: string;
  observableCache = new Map<string, Observable<Message[]> | null>();

  constructor() {
    this.playerId = this.authService.getUser()!.uid;
  }

  getMessages(roomId: string): Observable<Message[]> {
    if (environment.useColyseus) {
      // Ensure the game service is subscribed to the room, which initializes the message stream
      this.gameService.subscribeToRoom(roomId);
      return this.gameService.chatMessages$;
    }

    // Fallback to Firestore for the old method
    if (this.observableCache.get(roomId)) {
      return this.observableCache.get(roomId)!;
    }

    const res = new Observable<Message[]>(observer => {
      FirebaseFirestore.addCollectionSnapshotListener(
        {
          reference: `rooms/${roomId}/messages`,
          queryConstraints: [
            {
              type: 'orderBy',
              fieldPath: 'timestamp',
              directionStr: 'desc'
            },
            {
              type: 'limit',
              limit: 500
            }
          ]
        },
        (event, error) => {
          if (error) {
            console.log(error);
            observer.next(error);
          } else if (event?.snapshots == null) {
            console.log('event.snapshots is null');
            observer.next(undefined);
          } else {
            const messages: Message[] = [];
            event?.snapshots.forEach(doc => {
              messages.push({ id: doc.id, ...(doc.data as Message) });
            });
            observer.next(messages.reverse());
          }
        }
      );
    });
    this.observableCache.set(roomId, res);
    return res;
  }

  async sendMessage(roomId: string, text: string): Promise<void> {
    if (environment.useColyseus) {
      const room = this.gameService.getRoom();
      if (room) {
        room.send('sendChatMessage', { content: text });
      } else {
        console.error('Cannot send chat message: not in a Colyseus room.');
      }
      return;
    }

    const message: Message = {
      roomId: roomId,
      playerId: this.playerId,
      text: text,
      timestamp: serverTimestamp()
    };

    try {
      await FirebaseFirestore.addDocument({
        reference: `rooms/${roomId}/messages`,
        data: message
      });
    } catch (error) {
      console.error('Error adding message to Firestore:', error);
    }
  }

  async deleteMessage(roomId: string, messageId: string): Promise<void> {
    await FirebaseFirestore.deleteDocument({
      reference: `rooms/${roomId}/messages/${messageId}`
    });
  }
}

import {
  ApplicationConfig,
  inject,
  Injectable,
  provideAppInitializer,
  provideZoneChangeDetection
} from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';

import { routes } from './app.routes';
import { FirebaseApp, provideFirebaseApp } from '@angular/fire/app';
import { initializeApp } from '@firebase/app';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { TtsService } from './services/tts.service';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';
import { environment } from '../environments/environment';

// Injectable service to configure the Firebase Functions Emulator
@Injectable({
  providedIn: 'root'
})
export class FirebaseCapacitorConfig {
  async configureEmulator(): Promise<void> {
    if (!environment.production) {
      await FirebaseFunctions.useEmulator({
        host: window.location.hostname, // Use 'localhost' or '********' for Android emulator
        port: 5001
      });
      await FirebaseAuthentication.useEmulator({
        host: window.location.hostname,
        port: 9099
      });
      await FirebaseFirestore.useEmulator({
        host: window.location.hostname,
        port: 8080
      });
    }
  }
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(),
    provideFirebaseApp(() => initializeApp(environment.firebase)),
    provideAnimations(),
    provideAnimationsAsync(),
    provideAppInitializer(() => inject(TtsService).initialize()),
    FirebaseCapacitorConfig,
    provideAppInitializer(() => {
      inject(FirebaseApp);
      return inject(FirebaseCapacitorConfig).configureEmulator();
    })
  ]
};

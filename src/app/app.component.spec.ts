import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { Router, NavigationEnd } from '@angular/router';
import { AuthService } from './services/auth.service';
import { of, Subject } from 'rxjs';
import { cast } from './test/cast.mock';
import { RouterTestingModule } from '@angular/router/testing';
import { Component } from '@angular/core';

// Mock the cast object
(window as any).cast = cast;

@Component({ template: '' })
class MockLoginComponent {}

@Component({ template: '' })
class MockHomeComponent {}

describe('AppComponent', () => {
  let router: Router;
  let authService: jasmine.SpyObj<AuthService>;
  let routerEventsSubject: Subject<NavigationEnd>;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getAuthState'
    ]);
    routerEventsSubject = new Subject<NavigationEnd>();

    await TestBed.configureTestingModule({
      imports: [
        AppComponent,
        RouterTestingModule.withRoutes([
          { path: 'login', component: MockLoginComponent },
          { path: 'home', component: MockHomeComponent }
        ])
      ],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: Router,
          useValue: {
            events: routerEventsSubject.asObservable(),
            navigate: jasmine.createSpy('navigate'),
            url: '/home'
          }
        }
      ]
    }).compileComponents();

    router = TestBed.inject(Router);
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have the 'jeopardy-gpt' title`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.title).toEqual('jeopardy-gpt');
  });

  it('should navigate to login if user is not authenticated', fakeAsync(() => {
    const fixture = TestBed.createComponent(AppComponent);
    const component = fixture.componentInstance;
    authService.getAuthState.and.returnValue(of(null));
    fixture.detectChanges();

    routerEventsSubject.next(new NavigationEnd(1, '/home', '/home'));
    tick();

    expect(router.navigate).toHaveBeenCalledWith(['/login'], {
      queryParams: { redirect: '/home' }
    });
  }));

  it('should not navigate to login if user is authenticated', fakeAsync(() => {
    const fixture = TestBed.createComponent(AppComponent);
    const component = fixture.componentInstance;
    authService.getAuthState.and.returnValue(of({ uid: 'test' } as any));
    fixture.detectChanges();

    routerEventsSubject.next(new NavigationEnd(1, '/home', '/home'));
    tick();

    expect(router.navigate).not.toHaveBeenCalled();
  }));

  it('should not navigate to login if on the login page', fakeAsync(() => {
    const fixture = TestBed.createComponent(AppComponent);
    const component = fixture.componentInstance;
    authService.getAuthState.and.returnValue(of(null));
    (router as any).url = '/login';
    fixture.detectChanges();

    routerEventsSubject.next(new NavigationEnd(1, '/login', '/login'));
    tick();

    expect(router.navigate).not.toHaveBeenCalled();
  }));
});

.settings-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.settings-card {
  width: 80%;
  max-width: 500px;
  background-color: var(--mat-sys-color-surface);
  color: var(--mat-sys-color-on-surface);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timer-settings-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.timer-setting {
  display: flex;
  flex-direction: column;
}

.timer-label {
  font: var(--mat-sys-body-large);
  display: flex;
  align-items: center;
}

.info-icon {
  font-size: 16px;
  margin-left: 5px;
  color: var(--mat-sys-color-on-surface-variant);
}


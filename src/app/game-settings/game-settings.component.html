<div class="settings-overlay" (click)="closeSettings()">
  <mat-card class="settings-card" (click)="$event.stopPropagation()">
    <mat-card-header class="settings-header">
      <mat-card-title>Timer Settings</mat-card-title>
      <button mat-icon-button (click)="closeSettings()">
        <mat-icon>close</mat-icon>
      </button>
    </mat-card-header>
    <mat-card-content class="timer-settings-content">
      <div class="timer-setting" *ngIf="gameMode === 'BUZZER'">
        <div class="timer-label">
          Buzz-In Timer: {{ buzzInTimerDuration }} seconds
          <mat-icon
            class="info-icon"
            matTooltip="Time allowed for players to buzz in after a clue is revealed."
            >info</mat-icon
          >
        </div>
        <mat-slider
          min="5"
          max="120"
          step="5"
          discrete
          [displayWith]="formatSliderLabel"
          (change)="onTimerChange('buzzIn')"
          [color]="getSliderColor(buzzInTimerDuration, 5, 120)"
        >
          <input matSliderThumb [(ngModel)]="buzzInTimerDuration" />
        </mat-slider>
      </div>

      <div class="timer-setting">
        <div class="timer-label">
          Answer Timer: {{ answerDuration }} seconds
          <mat-icon
            class="info-icon"
            matTooltip="Time allowed for a player to provide an answer after buzzing in."
            >info</mat-icon
          >
        </div>
        <mat-slider
          min="5"
          max="120"
          step="5"
          discrete
          [displayWith]="formatSliderLabel"
          (change)="onTimerChange('answer')"
          [color]="getSliderColor(answerDuration, 5, 120)"
        >
          <input matSliderThumb [(ngModel)]="answerDuration" />
        </mat-slider>
      </div>

      <div class="timer-setting">
        <div class="timer-label">
          Show Answer Duration: {{ showAnswerDuration }} seconds
          <mat-icon
            class="info-icon"
            matTooltip="Time to display the answer before moving to the next clue."
            >info</mat-icon
          >
        </div>
        <mat-slider
          min="5"
          max="30"
          step="5"
          discrete
          [displayWith]="formatSliderLabel"
          (change)="onTimerChange('showAnswer')"
          [color]="getSliderColor(showAnswerDuration, 5, 30)"
        >
          <input matSliderThumb [(ngModel)]="showAnswerDuration" />
        </mat-slider>
      </div>

      <div class="timer-setting">
        <div class="timer-label">
          Initial Thinking Time: {{ initialThinkingDuration }} seconds
          <mat-icon
            class="info-icon"
            matTooltip="Additional time given to the first player to answer."
            >info</mat-icon
          >
        </div>
        <mat-slider
          min="0"
          max="60"
          step="5"
          discrete
          [displayWith]="formatSliderLabel"
          (change)="onTimerChange('initialThinking')"
          [color]="getSliderColor(initialThinkingDuration, 0, 60)"
        >
          <input matSliderThumb [(ngModel)]="initialThinkingDuration" />
        </mat-slider>
      </div>
    </mat-card-content>
  </mat-card>
</div>

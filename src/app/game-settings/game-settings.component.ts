import {
  Component,
  inject,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnDestroy,
  OnInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { GameService } from '../services/game.service';
import {
  Room,
  GameMode,
  LlmAgent,
} from '../../../functions/src/resources';
import { ThemePalette } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';

import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-game-settings',
  imports: [
    CommonModule,
    MatInputModule,
    FormsModule,
    MatCardModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    MatSliderModule,
    MatTooltipModule,
  ],
  templateUrl: './game-settings.component.html',
  styleUrls: ['./game-settings.component.css'],
  standalone: true,
})
export class GameSettingsComponent implements OnInit, OnChanges {
  @Input() room?: Room;
  @Input() isAdmin: boolean = false;
  @Output() close = new EventEmitter<void>();

  gameService: GameService = inject(GameService);
  authService: AuthService = inject(AuthService);
  snackbar: MatSnackBar = inject(MatSnackBar);

  isGamemasterMode: boolean = false;
  gameMode: string = 'BUZZER';

  readonly DEFAULT_BUZZ_IN_TIMER = 60;
  readonly DEFAULT_ANSWER_TIMER = 60;
  readonly DEFAULT_SHOW_ANSWER_TIMER = 10;
  readonly DEFAULT_INITIAL_THINKING_TIMER = 20;

  buzzInTimerDuration = this.DEFAULT_BUZZ_IN_TIMER;
  answerDuration = this.DEFAULT_ANSWER_TIMER;
  showAnswerDuration = this.DEFAULT_SHOW_ANSWER_TIMER;
  initialThinkingDuration = this.DEFAULT_INITIAL_THINKING_TIMER;

  timerSettingsModified = {
    buzzIn: false,
    answer: false,
    showAnswer: false,
    initialThinking: false,
  };

  ngOnInit() {
    this.ngOnChanges();
  }

  ngOnChanges() {
    if (!this.room) return;

    this.isGamemasterMode = this.room.mode === GameMode.GAMEMASTER;
    this.loadTimerSettingsFromRoom();
  }

  private loadTimerSettingsFromRoom(): void {
    if (this.room?.roomSettings) {
      this.buzzInTimerDuration = this.room.roomSettings.buzzInTimerDurationMillis / 1000;
      this.answerDuration = this.room.roomSettings.answerDurationMillis / 1000;
      this.showAnswerDuration = this.room.roomSettings.showAnswerDurationMillis / 1000;
      this.initialThinkingDuration = this.room.roomSettings.initialThinkingDurationMillis / 1000;

      if (this.room.roomSettings.gameMode === GameMode.BUZZER) {
        this.gameMode = 'BUZZER';
      } else if (this.room.roomSettings.gameMode === GameMode.TURN_BASED) {
        this.gameMode = 'TURN_BASED';
      }
    }
  }

  onTimerChange(timerType: string): void {
    if (!this.isAdmin || !this.room) return;

    const payload = {
      roomId: this.room.roomId,
      buzzInTimerDurationMillis: this.buzzInTimerDuration * 1000,
      answerDurationMillis: this.answerDuration * 1000,
      showAnswerDurationMillis: this.showAnswerDuration * 1000,
      initialThinkingDurationMillis: this.initialThinkingDuration * 1000,
    };

    this.gameService.call('updateRoomSettings', payload).catch((error) => {
      console.error('Error updating timer settings:', error);
    });
  }

  closeSettings() {
    this.close.emit();
  }

  formatSliderLabel(value: number): string {
    return `${value}s`;
  }

  getSliderColor(value: number, min: number, max: number): ThemePalette {
    const range = max - min;
    const third = range / 3;

    if (value <= min + third) {
      return 'warn';
    } else if (value >= max - third) {
      return 'primary';
    } else {
      return 'accent';
    }
  }
}

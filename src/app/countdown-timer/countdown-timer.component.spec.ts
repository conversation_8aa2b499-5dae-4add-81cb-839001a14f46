import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick
} from '@angular/core/testing';
import { CountdownTimerComponent } from './countdown-timer.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('CountdownTimerComponent', () => {
  let component: CountdownTimerComponent;
  let fixture: ComponentFixture<CountdownTimerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CountdownTimerComponent, NoopAnimationsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(CountdownTimerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should start countdown and update remaining time', fakeAsync(() => {
    component.startTime = Date.now();
    component.durationSeconds = 10;
    component.active = true;
    component.ngOnChanges({
      active: {
        currentValue: true,
        previousValue: false,
        firstChange: true,
        isFirstChange: () => true
      },
      startTime: {
        currentValue: component.startTime,
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });
    fixture.detectChanges();

    tick(5000);
    fixture.detectChanges();

    expect(component.remainingTime).toBe('00:05');

    tick(5000);
    fixture.detectChanges();

    expect(component.remainingTime).toBe('00:00');
  }));

  it('should set warning and critical time flags', fakeAsync(() => {
    component.startTime = Date.now();
    component.durationSeconds = 15;
    component.warningThreshold = 10;
    component.criticalThreshold = 5;
    component.active = true;
    component.ngOnChanges({
      active: {
        currentValue: true,
        previousValue: false,
        firstChange: true,
        isFirstChange: () => true
      },
      startTime: {
        currentValue: component.startTime,
        previousValue: undefined,
        firstChange: true,
        isFirstChange: () => true
      }
    });
    fixture.detectChanges();

    tick(5000);
    fixture.detectChanges();
    expect(component.isWarningTime).toBe(true);
    expect(component.isCriticalTime).toBe(false);

    tick(5000);
    fixture.detectChanges();
    expect(component.isWarningTime).toBe(true);
    expect(component.isCriticalTime).toBe(true);
  }));

  it('should emit control events', () => {
    spyOn(component.startTimer, 'emit');
    spyOn(component.resetTimer, 'emit');
    spyOn(component.pauseTimer, 'emit');
    spyOn(component.addTime, 'emit');
    spyOn(component.subtractTime, 'emit');

    component.onStartTimer();
    expect(component.startTimer.emit).toHaveBeenCalledWith(
      component.durationSeconds
    );

    component.onResetTimer();
    expect(component.resetTimer.emit).toHaveBeenCalled();

    component.onPauseTimer();
    expect(component.pauseTimer.emit).toHaveBeenCalled();

    component.onAddTime(10);
    expect(component.addTime.emit).toHaveBeenCalledWith(10);

    component.onSubtractTime(5);
    expect(component.subtractTime.emit).toHaveBeenCalledWith(5);
  });
});

<ng-container *ngIf="room$ | async as room">
  <app-top-bar [roomId]="room?.roomId" [isCast]="isCast"></app-top-bar>
  <app-waiting-room
    *ngIf="
      room &&
      (room!.gameState.gameProgress.type == 'WaitingRoom' ||
        room!.gameState.gameProgress.type == 'GeneratingQuestions')
    "
    [room]="room"
    [isCast]="isCast"
  ></app-waiting-room>

  <app-playing-room
    *ngIf="room && room!.gameState.gameProgress.type == 'RoomState'"
    [room]="room"
    [isCast]="isCast"
  ></app-playing-room>

  <app-winner-screen
    *ngIf="room && room!.gameState.gameProgress.type == 'RoomSummary'"
    [room]="room"
    [isCast]="isCast"
  ></app-winner-screen>

  <app-chat-room
    *ngIf="amIPartOfRoom(room) && !isCast"
    [room]="room"
  ></app-chat-room>
</ng-container>

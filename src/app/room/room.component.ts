import {
  ChangeDetectorRef,
  Component,
  inject,
  Ng<PERSON><PERSON>,
  OnInit
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { WaitingRoomComponent } from '../waiting-room/waiting-room.component';
import { Room, Round } from '@functions/resources';
import { distinctUntilChanged, map, Subscription, Observable, switchMap, of } from 'rxjs';
import { CommonModule } from '@angular/common';
import { PlayingRoomComponent } from '../playing-room/playing-room.component';
import { ChatRoomComponent } from '../chat-room/chat-room.component';
import { AuthService } from '../services/auth.service';
import { TopBarComponent } from '../top-bar/top-bar.component';
import { WinnerScreenComponent } from '../winner-screen/winner-screen.component';
import { GameService } from '../services/game.service';

@Component({
  selector: 'app-room',
  imports: [
    WaitingRoomComponent,
    CommonModule,
    PlayingRoomComponent,
    Chat<PERSON>oomComponent,
    TopBarComponent,
    WinnerScreenComponent
  ],
  templateUrl: './room.component.html',
  styleUrl: './room.component.css'
})
export class RoomComponent implements OnInit {
  room$: Observable<Room | null>;
  currentRound?: Round;
  authService = inject(AuthService);
  gameService = inject(GameService);
  route: ActivatedRoute = inject(ActivatedRoute);

  isCast: boolean = false;
  private routeSub: Subscription;

  ngOnDestroy(): void {
    this.routeSub.unsubscribe(); // IMPORTANT: Prevent memory leaks
  }
  constructor() {
    this.room$ = of(null); // Initialize with a null observable
    this.routeSub = this.route.data.subscribe(data => {
      this.isCast = data['isCast']; // Access the data
    });
  }
  ngOnInit() {
    if (this.isCast) {
      this.room$ = this.route.params
        .pipe(
          map(params => params['roomId']),
          distinctUntilChanged(),
          switchMap(roomId => { // Use switchMap to flatten the observable
            if (!roomId) {
              return of(null); // Return an observable of null if no roomId
            }
            return this.gameService.subscribeToRoom(roomId, { isSpectator: true });
          }),
        );
    } else {
      this.room$ = this.route.params
        .pipe(
          map(params => params['roomId']),
          distinctUntilChanged(),
          switchMap(roomId => { // Use switchMap to flatten the observable
            if (!roomId) {
              return of(null); // Return an observable of null if no roomId
            }
            return this.gameService.subscribeToRoom(roomId);
          }),
        );
    }
  }

  amIPartOfRoom(room: Room): boolean {
    return (
      room.playerIds.some(id => id === this.authService.getUser()?.uid) ||
      false
    );
  }
}

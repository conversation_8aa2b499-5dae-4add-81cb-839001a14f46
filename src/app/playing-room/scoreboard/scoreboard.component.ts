import {
  Component,
  inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  Clue,
  ClueState,
  GameMode,
  Player,
  Room,
  RoomState
} from '../../../../functions/src/resources'; // Update with your actual path
import { TimerChipComponent } from '../../timer-chip/timer-chip.component';
import {
  trigger,
  state,
  style,
  transition,
  animate
} from '@angular/animations';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GameService } from '../../services/game.service';
import { AuthService } from '../../services/auth.service';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-scoreboard',
  standalone: true,
  imports: [
    CommonModule,
    TimerChipComponent,
    MatCardModule,
    MatTooltipModule,
    MatIconModule
  ], // For *ngFor, *ngIf, etc.
  templateUrl: './scoreboard.component.html',
  styleUrls: ['./scoreboard.component.css'],
  animations: [
    trigger('playerAnimation', [
      state(
        'buzzed',
        style({
          backgroundColor: '{{buzzedColor}}'
        }),
        { params: { buzzedColor: '#ffcc00' } }
      ),
      state(
        'notBuzzed',
        style({
          backgroundColor: 'transparent'
        })
      ),
      transition('notBuzzed => buzzed', [animate('0.3s ease-out')]),
      transition('buzzed => notBuzzed', [animate('0.2s ease-in')])
    ])
  ]
})
export class ScoreboardComponent implements OnInit, OnChanges {
  @Input() room?: Room;
  @Input() isCast: boolean = false;

  currentPlayerId: string | undefined;
  currentAnswerPlayerId: string | undefined;
  clueState?: ClueState;
  roomState?: RoomState;
  answerStartTime?: number;
  answerTargetTime?: number;
  isGamemasterMode = false;

  gameService = inject(GameService);
  authService = inject(AuthService);
  ngOnInit() {}

  ngOnChanges(): void {
    this.isGamemasterMode = this.room?.mode == GameMode.GAMEMASTER;
    if (
      this.room &&
      this.room.gameState &&
      this.room.gameState.gameProgress.type === 'RoomState'
    ) {
      this.roomState = this.room.gameState.gameProgress as RoomState;
      const roundState = this.roomState.roundStates[this.roomState.roundIdx];
      this.currentAnswerPlayerId = undefined;
      this.currentPlayerId = this.room.playerIds[
        this.roomState.currentPlayerIdx
      ];
      if (roundState && roundState.currentClueIdx != undefined && roundState.currentCategoryIdx != undefined) {
        const categoryState = roundState.categoryStates[roundState.currentCategoryIdx];
        if (categoryState) {
          this.clueState = categoryState.clueStates[roundState.currentClueIdx];
          this.answerStartTime = this.clueState.answerStartTime;
          this.answerTargetTime =
            this.clueState?.answerStartTime! +
            this.room?.roomSettings!.answerDurationMillis;
          if (this.clueState!.queueAnswerTurnIdx == 0) {
            this.answerTargetTime += this.room?.roomSettings!.initialThinkingDurationMillis;
          }
          if (!this.clueState.showAnswerStartTime) {
            this.currentAnswerPlayerId = this.clueState.buzzedInPlayerQueue[
              this.clueState.queueAnswerTurnIdx
            ];
          }
        } else {
          this.clueState = undefined;
        }
      } else {
        this.clueState = undefined;
      }
    }
  }

  getScoreTooltip(score: number): string {
    if (score > 0) {
      return `Current score: ${score} points`;
    } else if (score === 0) {
      return 'No points yet';
    } else {
      return `Negative score: ${score} points`;
    }
  }
}

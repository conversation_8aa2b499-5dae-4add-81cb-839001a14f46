import { Component, Input, OnChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  Round,
  RoundState,
  Room,
  GameMode,
  RoomState
} from '../../../../functions/src/resources';
import { GameService } from '../../services/game.service';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-board',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './board.component.html',
  styleUrls: ['./board.component.css']
})
export class BoardComponent implements OnChanges {
  @Input() room?: Room;
  @Input() round?: Round;
  @Input() isCast: boolean = false;

  gameService: GameService = inject(GameService);
  authService = inject(AuthService);

  isGamemasterMode: boolean = false;
  isAdmin: boolean = false;
  roomState?: RoomState;
  roundState?: RoundState;
  initialRenderComplete: boolean = false;

  constructor() {}

  ngOnChanges(): void {
    this.roomState = this.room!.gameState.gameProgress as RoomState;
    this.roundState = this.roomState.roundStates[this.roomState.roundIdx];
    this.isAdmin = this.authService.getUser()?.uid === this.room?.host;
    this.isGamemasterMode = this.room?.mode === GameMode.GAMEMASTER;

    // Set flag after first render to prevent animations on subsequent renders
    setTimeout(() => {
      this.initialRenderComplete = true;
    }, 1000); // Wait for animations to complete
  }

  isClueCompleted(categoryIdx: number, clueIdx: number): boolean {
    const clueState = this.roundState?.categoryStates?.[categoryIdx]?.clueStates?.[clueIdx];
    // If the state is not available for any reason, treat it as completed to be safe.
    return clueState?.clueComplete ?? true;
  }

  canSelectClue(categoryIdx: number, clueIdx: number): boolean {
    if (this.isClueCompleted(categoryIdx, clueIdx)) {
      return false;
    }

    // If a clue is already active, nobody can select another one.
    if (this.roundState?.currentClueIdx !== -1) {
      return false;
    }

    // In Gamemaster mode, only the admin can select clues.
    if (this.isGamemasterMode) {
      return this.isAdmin;
    }

    // In other modes, only the current player can select a clue.
    const isMyTurn =
      this.room?.playerIds[this.roomState!.currentPlayerIdx] ===
      this.authService.getUser()?.uid;

    return isMyTurn;
  }

  async selectClue(categoryIdx: number, clueIdx: number) {
    if (!this.canSelectClue(categoryIdx, clueIdx)) {
      return;
    }

    try {
      await this.gameService.call('selectClue', {
        roomId: this.room!.roomId,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx,
        clueIdx
      });
    } catch (e) {
      console.error('Failed to select clue:', e);
      // Handle error, maybe show a toast to the user
    }
  }

  isPending(categoryIdx: number, clueIdx: number) {
    return (
      this.roundState?.currentCategoryIdx == categoryIdx &&
      this.roundState?.currentClueIdx == clueIdx
    );
  }
  getClueState(categoryIdx: number, clueIdx: number) {
    return this.roundState?.categoryStates[categoryIdx].clueStates[clueIdx];
  }
  getClue(categoryIdx: number, clueIdx: number) {
    return this.round?.categories[categoryIdx].clues[clueIdx];
  }

  getPlayerName(playerId: string) {
    return this.room?.playerInfos[
      this.room?.playerIds.findIndex(p => p == playerId)!
    ].userName;
  }

  getClueTooltip(categoryIdx: number, clueIdx: number): string {
    if (this.isClueCompleted(categoryIdx, clueIdx)) {
      if (this.isPending(categoryIdx, clueIdx)) {
        return 'This clue is currently active';
      }

      const clueState = this.getClueState(categoryIdx, clueIdx);
      if (clueState?.answeredByPlayerId) {
        const playerName = this.getPlayerName(clueState.answeredByPlayerId);
        return `Answered correctly by ${playerName}`;
      } else {
        return 'No one answered this clue correctly';
      }
    }

    if (this.canSelectClue(categoryIdx, clueIdx)) {
       return `Select this ${
        this.getClue(categoryIdx, clueIdx)?.value
      } point clue`;
    }

    return 'It is not your turn to select a clue.';
  }
}


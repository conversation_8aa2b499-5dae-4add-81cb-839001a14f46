.board-container {
  display: flex;
  justify-content: flex-start; /* Center the board horizontally */
  align-items: center; /* Center the board vertically if there's extra space */
  height: 100%; /* Use full height */
  flex-grow: 1;
  background-color: var(--mat-sys-surface);
  width: 100%;
  max-width: 100vw;
  padding: 0.5rem;
  /* Add subtle pattern background that works in both light and dark modes */
  background-image: radial-gradient(
    color-mix(in srgb, var(--mat-sys-on-surface) 20%, transparent),
    transparent 1px
  );
  background-size: 1rem 1rem;
  /* Only animate on initial load, not on re-renders */
  animation: fadeIn 0.5s ease-in-out 1; /* The '1' means play only once */
  animation-fill-mode: forwards; /* Keep the final state */
  -webkit-overflow-scrolling: touch; /* Improve scrolling on iOS */
}

.board {
  display: grid;
  grid-template-columns: repeat(
    var(--num-cols),
    minmax(150px, 250px)
  ); /* Reduced minimum width for TV */
  gap: 0.2rem; /* Reduced gap for better space utilization */
  height: 80%;
  min-width: min-content; /* Ensure the grid doesn't shrink below column sizes */
  /* Add subtle shadow for depth */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 0.25rem;
  margin: 0 auto; /* Center the board when there's enough space */
  border-radius: 0.75rem;
  background-color: rgba(var(--mat-sys-primary-rgb), 0.05);
}

.board-header-row {
  display: contents; /* Maintain grid layout integration */
}

.board-header-cell {
  height: calc(100% - 20px);
  padding: 10px;
  padding-left: 0; /* Ensure no left padding for first column */
  animation: slideInUp 0.4s ease-out 1; /* Play only once */
  animation-fill-mode: forwards; /* Keep the final state */
  box-sizing: border-box;
}

.board-header-card {
  height: 100%;
  width: 100%;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--mat-sys-tertiary) 30%, var(--mat-sys-primary)),
    var(--mat-sys-primary)
  );
  color: var(--mat-sys-on-primary);
  border-radius: 1rem;
  font: var(--mat-sys-title-large);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 4px 8px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.board-header-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
}

.board-row {
  display: contents; /* Maintain grid layout integration */
}

.board-row-cell {
  height: 100%;
  padding: 4px;
  padding-left: 0; /* Ensure no left padding for first column */
  animation: slideInUp 0.4s ease-out 1; /* Play only once */
  animation-fill-mode: forwards; /* Keep the final state */
  animation-delay: calc(
    var(--animation-order, 0) * 0.05s
  ); /* Staggered animation */
  box-sizing: border-box;
}

.clue-card {
  width: 100%;
  height: 100%;
  border: 1px solid transparent; /* Add transparent border to prevent layout shift */
  border-radius: 1rem;
  cursor: pointer;
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    background 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.1s ease; /* Faster color transition */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row; /* Ensure flex items are displayed in a row */
  flex-wrap: nowrap; /* Prevent wrapping */
  font: var(--mat-sys-title-large);
  font-weight: 600;
  background-color: var(--mat-sys-primary-container);
  /* Add diagonal lines effect - with fixed size instead of percentage */
  background-image: linear-gradient(
      135deg,
      color-mix(in srgb, var(--mat-sys-on-primary-container) 15%, transparent)
        0%,
      transparent 25%
    ),
    linear-gradient(
      315deg,
      color-mix(in srgb, var(--mat-sys-tertiary) 10%, transparent) 0%,
      transparent 25%
    );
  color: var(--mat-sys-on-primary-container);
  box-shadow: 0 2px 5px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
  position: relative;
  overflow: hidden;
  box-sizing: border-box; /* Ensure border is included in width/height calculations */
}

/* Add subtle shine effect - with responsive sizing */
.clue-card::after {
  content: "";
  position: absolute;
  width: 200%;
  aspect-ratio: 1 / 1;
  background: linear-gradient(
    to bottom right,
    transparent 0%,
    color-mix(in srgb, var(--mat-sys-on-primary-container) 10%, transparent) 50%,
    transparent 100%
  );
  transform: translate(-85%) rotate(45deg);
  transition: transform 0.5s ease-out;
}

.clue-card:hover {
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--mat-sys-primary) 90%, transparent),
    color-mix(in srgb, var(--mat-sys-tertiary) 60%, var(--mat-sys-primary) 40%)
  );
  /* Preserve diagonal lines effect on hover - with fixed size */
  background-image: linear-gradient(
      135deg,
      color-mix(in srgb, var(--mat-sys-on-primary) 20%, transparent) 0%,
      transparent 25%
    ),
    linear-gradient(
      315deg,
      color-mix(in srgb, var(--mat-sys-tertiary) 25%, transparent) 0%,
      transparent 25%
    );
  color: var(--mat-sys-on-primary-container); /* Use Material Design variable */
  font-weight: 700; /* Make it bolder for better visibility */
  text-shadow: 0 1px 2px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent); /* Add shadow for better readability */
  transform: translateY(-3px);
  box-shadow: 0 8px 20px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  border: 1px solid
    color-mix(in srgb, var(--mat-sys-on-primary) 30%, transparent);
}

.clue-card:hover::after {
  transform: translate(-50%) rotate(45deg);
}

.clue-card:disabled,
.clue-card.clue-completed {
  background-color: color-mix(in srgb, var(--mat-sys-surface-variant) 50%, transparent);
  color: var(--mat-sys-on-surface-variant);
  cursor: default;
  box-shadow: none;
  transform: none;
  background-image: none; /* Remove gradient from completed clues */
}

.clue-card.clue-completed:hover {
    background-color: color-mix(in srgb, var(--mat-sys-surface-variant) 50%, transparent);
    color: var(--mat-sys-on-surface-variant);
    cursor: default;
    box-shadow: none;
    transform: none;
    background-image: none; /* Remove gradient from completed clues */
}

.clue-card.not-my-turn {
  cursor: not-allowed;
  opacity: 0.7;
  /* Keep some of the interactive look but indicate it's not clickable */
  box-shadow: 0 1px 3px color-mix(in srgb, var(--mat-sys-shadow) 15%, transparent);
}

.clue-card.not-my-turn:hover {
  transform: none; /* No lift effect */
  box-shadow: 0 1px 3px color-mix(in srgb, var(--mat-sys-shadow) 15%, transparent);
}


.category-title-text {
  padding: 0.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Add icon animations */
.icon {
  animation: fadeIn 0.3s ease-out 1; /* Play only once */
  animation-fill-mode: forwards; /* Keep the final state */
}

.correct-container {
  display: flex;
  flex-direction: row; /* Change to row to display items inline */
  align-items: center;
  justify-content: center;
  animation: pulse 0.5s ease-out 1; /* Play only once */
  animation-fill-mode: forwards; /* Keep the final state */
  width: 100%; /* Ensure container takes full width */
  padding: 0 4px; /* Add padding to prevent text from touching edges */
  box-sizing: border-box; /* Include padding in width calculation */
}
.icon {
  margin-left: 5px;
  margin-right: 5px;
}

.correct-player {
  font-style: italic;
  font-size: var(--mat-sys-body-small);
  margin-left: 4px; /* Add a small margin to separate from the icon */
  margin-right: 4px; /* Add right margin to prevent text from being cut off */
  display: inline-block; /* Changed to inline-block for better text handling */
  white-space: nowrap; /* Prevent text from wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflow text */
  max-width: 120px; /* Increased width to prevent cutting off characters */
  padding-right: 2px; /* Add padding to ensure the last character is fully visible */
}

/* Class to disable animations after initial render */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

/* Media query for mobile devices */
@media (max-width: 768px) {
  .board {
    grid-template-columns: repeat(
      var(--num-cols),
      minmax(180px, 250px)
    ); /* Even smaller for mobile */
    min-width: min-content; /* Ensure the grid doesn't shrink below column sizes */
  }

  .board-header-card {
    font: var(--mat-sys-title-medium); /* Smaller font for mobile */
  }

  .clue-card {
    font: var(--mat-sys-title-medium); /* Smaller font for mobile */
  }
}

/* Media query for TV screens (16:9 aspect ratio) */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .board {
    grid-template-columns: repeat(
      var(--num-cols),
      minmax(120px, 1fr)
    ); /* Optimized for TV */
    height: 100%;
  }

  .board-header-card {
    font: var(--mat-sys-title-medium);
    padding: 0.25rem;
  }

  .clue-card {
    font: var(--mat-sys-title-medium);
  }
}

/* Cast mode specific styles */
:host-context(.is-cast) .board-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

:host-context(.is-cast) .board {
  display: grid;
  /* grid-template-columns: repeat(var(--num-cols), minmax(100px, 1fr)); */
  gap: 0.25rem;
}

:host-context(.is-cast) .board-header-card {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

:host-context(.is-cast) .clue-card {
  font-size: clamp(0.8rem, 2vw, 1.2rem);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

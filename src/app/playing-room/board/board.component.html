<div
  *ngIf="round"
  class="board-container"
  [class.no-animations]="initialRenderComplete"
  [class.is-cast]="isCast"
>
  <div class="board" [style.--num-cols]="round.categories!.length">
    <div class="board-header-row">
      <div
        class="board-header-cell"
        mat-raised-button
        *ngFor="let category of round!.categories"
      >
        <div
          class="board-header-card"
          matTooltip="{{ category.categoryDescription }}"
        >
          <div class="category-title-text">
            {{ category.categoryTitle }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="board-row"
      *ngFor="let clue of round.categories![0].clues; let clueIdx = index"
    >
      <div
        class="board-row-cell"
        *ngFor="let category of round?.categories; let categoryIdx = index"
      >
        <button
          mat-fab
          extended
          (click)="selectClue(categoryIdx, clueIdx)"
          class="clue-card"
          [ngClass]="{
            'clue-completed': isClueCompleted(categoryIdx, clueIdx),
            'not-my-turn':
              !canSelectClue(categoryIdx, clueIdx) &&
              !isClueCompleted(categoryIdx, clueIdx) &&
              !isCast
          }"
          [matTooltip]="getClueTooltip(categoryIdx, clueIdx)"
        >
          <div *ngIf="!isClueCompleted(categoryIdx, clueIdx)">
            {{ getClue(categoryIdx, clueIdx)?.value }}
          </div>
          <!-- If it's current clue -->
          <mat-icon *ngIf="isPending(categoryIdx, clueIdx)" class="icon"
            >pending</mat-icon
          >
          <!-- If it wasn't answered successfully  -->
          <mat-icon
            *ngIf="
              !getClueState(categoryIdx, clueIdx)!.answeredByPlayerId &&
              isClueCompleted(categoryIdx, clueIdx) &&
              !isPending(categoryIdx, clueIdx)
            "
            class="icon"
            >cancel</mat-icon
          >
          <!-- If it was answered successfully  -->
          <div
            *ngIf="getClueState(categoryIdx, clueIdx)!.answeredByPlayerId"
            class="correct-container"
          >
            <mat-icon class="icon">check_circle</mat-icon>
            <span class="correct-player">
              {{
                getPlayerName(
                  getClueState(categoryIdx, clueIdx)!.answeredByPlayerId!
                )
              }}
            </span>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

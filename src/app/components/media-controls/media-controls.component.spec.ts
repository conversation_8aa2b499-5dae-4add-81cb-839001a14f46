import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MediaControlsComponent } from './media-controls.component';
import {
  MediaControlService,
  MediaElement
} from '../../services/media-control.service';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('MediaControlsComponent', () => {
  let component: MediaControlsComponent;
  let fixture: ComponentFixture<MediaControlsComponent>;
  let mediaControlServiceSpy: jasmine.SpyObj<MediaControlService>;

  beforeEach(async () => {
    mediaControlServiceSpy = jasmine.createSpyObj('MediaControlService', [
      'getAllMediaElements',
      'isPlaying',
      'playMedia',
      'pauseMedia',
      'pauseAllMedia',
      'currentlyPlaying$'
    ]);
    mediaControlServiceSpy.currentlyPlaying$ = of('');
    mediaControlServiceSpy.getAllMediaElements.and.returnValue([]);

    await TestBed.configureTestingModule({
      imports: [MediaControlsComponent, NoopAnimationsModule],
      providers: [
        { provide: MediaControlService, useValue: mediaControlServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MediaControlsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display media elements', () => {
    const mediaElements: MediaElement[] = [
      { id: '1', element: {} as any, src: 'video.mp4', type: 'video' },
      { id: '2', element: {} as any, src: 'audio.mp3', type: 'audio' }
    ];
    mediaControlServiceSpy.getAllMediaElements.and.returnValue(mediaElements);
    component['updateMediaElements']();
    fixture.detectChanges();
    const mediaItems = fixture.nativeElement.querySelectorAll('.media-item');
    expect(mediaItems.length).toBe(2);
  });

  it('should toggle play/pause', () => {
    mediaControlServiceSpy.isPlaying.and.returnValue(false);
    component.togglePlayPause('1');
    expect(mediaControlServiceSpy.playMedia).toHaveBeenCalledWith('1');

    mediaControlServiceSpy.isPlaying.and.returnValue(true);
    component.togglePlayPause('1');
    expect(mediaControlServiceSpy.pauseMedia).toHaveBeenCalledWith('1');
  });

  it('should pause all media', () => {
    component.pauseAll();
    expect(mediaControlServiceSpy.pauseAllMedia).toHaveBeenCalled();
  });
});
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PlyrControlsComponent } from './plyr-controls.component';
import { PlyrService, PlyrInstance } from '../../services/plyr.service';
import { CastMediaControlService } from '../../services/cast-media-control.service';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('PlyrControlsComponent', () => {
  let component: PlyrControlsComponent;
  let fixture: ComponentFixture<PlyrControlsComponent>;
  let plyrServiceSpy: jasmine.SpyObj<PlyrService>;
  let castMediaControlServiceSpy: jasmine.SpyObj<CastMediaControlService>;

  beforeEach(async () => {
    plyrServiceSpy = jasmine.createSpyObj('PlyrService', [
      'getAllPlayers',
      'getPlayer',
      'setVolume',
      'currentlyPlaying$'
    ]);
    plyrServiceSpy.currentlyPlaying$ = of('');
    plyrServiceSpy.getAllPlayers.and.returnValue([]);
    castMediaControlServiceSpy = jasmine.createSpyObj(
      'CastMediaControlService',
      [
        'sendPlayCommand',
        'sendPauseCommand',
        'sendSeekCommand',
        'sendVolumeCommand',
        'sendMuteCommand',
        'sendUnmuteCommand',
        'sendPauseAllCommand'
      ]
    );

    await TestBed.configureTestingModule({
      imports: [PlyrControlsComponent, NoopAnimationsModule],
      providers: [
        { provide: PlyrService, useValue: plyrServiceSpy },
        {
          provide: CastMediaControlService,
          useValue: castMediaControlServiceSpy
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PlyrControlsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display players', () => {
    const players: PlyrInstance[] = [
      {
        id: '1',
        player: {} as any,
        element: {} as any,
        type: 'video',
        questionId: 'q1',
        src: 'video.mp4',
        title: 'Video'
      },
      {
        id: '2',
        player: {} as any,
        element: {} as any,
        type: 'audio',
        questionId: 'q2',
        src: 'audio.mp3',
        title: 'Audio'
      }
    ];
    plyrServiceSpy.getAllPlayers.and.returnValue(players);
    component['updatePlayersList']();
    fixture.detectChanges();
    const playerItems = fixture.nativeElement.querySelectorAll('.player-item');
    expect(playerItems.length).toBe(2);
  });

  it('should toggle play/pause', () => {
    component.roomId = 'test-room';
    component.togglePlayPause('1');
    expect(
      castMediaControlServiceSpy.sendPlayCommand
    ).toHaveBeenCalledWith('test-room', '1');

    (component as any).castPlayingStates.set('1', true);
    component.togglePlayPause('1');
    expect(
      castMediaControlServiceSpy.sendPauseCommand
    ).toHaveBeenCalledWith('test-room', '1');
  });

  it('should seek to start', () => {
    component.roomId = 'test-room';
    const player: PlyrInstance = {
      id: '1',
      player: {} as any,
      element: document.createElement('video'),
      type: 'video',
      questionId: 'q1',
      src: 'video.mp4',
      title: 'Video'
    };
    player.element.setAttribute('data-media-start', '10');
    plyrServiceSpy.getAllPlayers.and.returnValue([player]);
    component.seekToStart('1');
    expect(
      castMediaControlServiceSpy.sendSeekCommand
    ).toHaveBeenCalledWith('test-room', '1', 10);
  });

  it('should set volume', () => {
    component.roomId = 'test-room';
    component.setVolume('1', 0.5);
    expect(plyrServiceSpy.setVolume).toHaveBeenCalledWith('1', 0.5);
    expect(
      castMediaControlServiceSpy.sendVolumeCommand
    ).toHaveBeenCalledWith('test-room', '1', 0.5);
  });

  it('should toggle mute', () => {
    component.roomId = 'test-room';
    component.toggleMute('1');
    expect(
      castMediaControlServiceSpy.sendMuteCommand
    ).toHaveBeenCalledWith('test-room', '1');

    (component as any).castMutedStates.set('1', true);
    component.toggleMute('1');
    expect(
      castMediaControlServiceSpy.sendUnmuteCommand
    ).toHaveBeenCalledWith('test-room', '1');
  });

  it('should pause all', () => {
    component.roomId = 'test-room';
    component.pauseAll();
    expect(castMediaControlServiceSpy.sendPauseAllCommand).toHaveBeenCalledWith(
      'test-room'
    );
  });
});
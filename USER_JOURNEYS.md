# User Journeys

This document outlines the key user flows for each game mode.

---

## 1. Buzzer Mode (Fast-Paced)

This mode is a race to buzz in first.

### Clue Selection
1.  A player selects a clue.
2.  **`handleSelectClue()`** is triggered.
3.  The clue is displayed to all players, and a buzz-in timer starts.

### Buzzing In
1.  Players click the "Buzz" button.
2.  **`handleBuzzIn()`** is triggered for each buzz.
3.  The first player is added to the `buzzedInPlayerQueue` and their answer timer starts immediately. The main buzz-in timer is cleared.

### Answering
1.  The answering player submits a guess.
2.  **`handleSubmitGuess()`** is triggered.
3.  **If correct:** The player's score is updated, the clue is marked complete, and the answer is revealed.
4.  **If incorrect:** The player is removed from the queue. If the queue is not empty, the next player gets a chance to answer.

### Passing
1.  The answering player clicks "Pass".
2.  A `handlePass()` function (or logic within `handleSubmitGuess`) is triggered.
3.  The player is added to a `passedPlayers` list for the clue.
4.  The turn passes to the next player in the buzz-in queue, or the clue ends if the queue is empty.

### Timer Expirations
-   **`handleBuzzInTimerExpired()`**: If the initial buzz-in timer runs out and no one has buzzed in, the clue ends and the answer is shown.
-   **`handleGuessTimerExpired()`**: If a player's answer timer runs out, their guess is marked incorrect, and the turn passes to the next player in the queue.

---

## 2. Turn-Based Mode (Strategic)

Players take turns selecting and answering clues in a fixed order.

### Clue Selection
1.  The current player selects a clue.
2.  **`handleSelectClue()`** is triggered.
3.  The clue is displayed, and an "initial thinking" timer starts for the current player.

### Answering
1.  The current player submits a guess.
2.  **`handleSubmitGuess()`** is triggered.
3.  **If correct:** The player's score is updated, the clue is marked complete, and the turn moves to the next player in the overall game order.
4.  **If incorrect:** The turn moves to the *next player in the answer queue* for that specific clue.

### Passing
1.  The current player clicks "Pass".
2.  A `handlePass()` function is triggered.
3.  The turn moves to the next player in the answer queue for that clue. If the queue is empty, the clue ends, and the turn moves to the next player in the overall game order.

---

## 3. Gamemaster Mode (Host-Controlled)

The host controls the entire flow of the game. There are no automatic timers.

### Clue Selection
1.  The gamemaster selects a clue.
2.  **`handleSelectClue()`** is triggered, displaying the clue to all players.

### Gamemaster Actions
-   **Show/Hide Hint:**
    -   **Function:** `handleGameMasterAction()` with a `TOGGLE_HINT` action.
    -   **Behavior:** Toggles the `hintOpened` flag on the clue state.
-   **Show/Hide Answer:**
    -   **Function:** `handleGameMasterAction()` with a `TOGGLE_ANSWER` action.
    -   **Behavior:** Toggles the `showAnswer` flag. Revealing the answer marks the clue as complete.
-   **Assign Points:**
    -   **Function:** `handleAssignPoints()`
    -   **Behavior:** Manually awards points to a selected team and marks the clue as complete.
-   **Regenerate Clue:**
    -   **Function:** `handleRegenerateClue()`
    -   **Behavior:** Requests a new question from the LLM for the current clue slot.
-   **Skip Answer Reveal:**
    -   **Function:** `handleSkipShowAnswer()`
    -   **Behavior:** Cancels the "show answer" timer and allows the next clue to be selected immediately.

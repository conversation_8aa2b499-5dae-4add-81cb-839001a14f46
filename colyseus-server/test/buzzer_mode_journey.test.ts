import { ColyseusTestServer, boot } from '@colyseus/testing';
import { strict as assert } from 'assert';
import { JeopardyRoom } from '../src/rooms/JeopardyRoom';
import { GameMode, RoomSettings } from '../src/room_schema';
import appConfig from '../src/app.config';
import { LlmService } from '../src/llm_service';
import { FakeDatabase } from '../src/fake_database';
import { IAuth } from '../src/auth';
import { delay, FakeAuthService, waitForCondition } from './test_helpers';
import { DecodedIdToken } from 'firebase-admin/auth';
import { ServiceRegistry } from '../src/services';

class MockLlmServiceForBuzzer extends LlmService {
  override async generate(prompt: string): Promise<string> {
    const fakeApiResponse = [
      {
        categoryTitle: 'Buzzer Category',
        questions: [
          {
            answer: 'Buzzer Answer',
            value: 200,
            questionHTML: '<div><PERSON>er Question?</div>',
            hint: 'Buzzer Hint',
            detailedFactsAboutAnswer: ['Fact 1']
          }
        ]
      }
    ];
    return '```json\n' + JSON.stringify(fakeApiResponse) + '\n```';
  }
}

describe('Buzzer Mode Journey', () => {
  let colyseus: ColyseusTestServer;
  let db: FakeDatabase;

  beforeEach(async () => {
    db = new FakeDatabase();
    ServiceRegistry.initialize({
      database: db,
      auth: new FakeAuthService(),
      llm: new MockLlmServiceForBuzzer()
    });
    colyseus = await boot(appConfig);
  });

  afterEach(async () => {
    await colyseus.shutdown();
    ServiceRegistry.destroy();
  });

  it('should handle a full buzzer mode flow', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'player1-uid-token' });
    const player2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    room.state.roomSettings.buzzInTimerDurationMillis = 100; // 100 ms

    // Start game
    host.send('startRoom');
    await room.waitForNextPatch(); // GeneratingQuestions
    await room.waitForNextPatch(); // RoomState

    assert.equal(room.state.gameState.progressType, 'RoomState');

    // Select a clue
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    await delay(2200);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].hintOpened,
      2500
    );

    let clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.hintOpened,
      true,
      'Hint should be opened after the first timeout'
    );

    // Wait for the second buzz-in timer to expire
    await delay(110);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].showAnswer,
      2500
    );

    clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.showAnswer,
      true,
      'Answer should be shown after the second timeout'
    );

    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].clueComplete,
      2500
    );
    assert.equal(
      clueState.clueComplete,
      true,
      'Clue should be complete after the second timeout'
    );
  });

  it('should handle a correct buzz-in flow', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    room.state.roomSettings.buzzInTimerDurationMillis = 5000;

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    assert.equal(room.state.gameState.progressType, 'RoomState');

    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    let clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.buzzedInPlayerQueue[0], 'player1-uid');

    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Buzzer Answer'
    });
    await room.waitForNextPatch();

    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, 200);
  });

  it('should handle the two-phase buzzer mode flow correctly', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    const player2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    room.state.roomSettings.buzzInTimerDurationMillis = 2000;
    room.state.roomSettings.answerDurationMillis = 2000;
    room.state.roomSettings.showAnswerDurationMillis = 2000;

    // Start game
    host.send('startRoom');
    await room.waitForNextPatch(); // GeneratingQuestions
    await room.waitForNextPatch(); // RoomState

    // Select a clue
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Player 1 buzzes in and answers incorrectly
    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Wrong Answer'
    });
    await room.waitForNextPatch();

    // Check that player 1's score is deducted
    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, -200);

    // Wait for the first buzz-in timer to expire, which should trigger the hint phase
    await delay(2100);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].hintOpened,
      2200
    );

    let clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.hintOpened,
      true,
      'Hint should be opened after the first buzz-in phase'
    );

    // Player 2 buzzes in and answers correctly
    player2.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    player2.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Buzzer Answer'
    });
    await room.waitForNextPatch();

    // Check that player 2's score is awarded (half points because hint was opened)
    const player2Info = room.state.playerInfos.find(
      p => p.id === 'player2-uid'
    );
    assert.equal(player2Info.score, 100);

    // Check that the clue is now complete
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].clueComplete,
      2200
    );
    clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.clueComplete,
      true,
      'Clue should be complete after a correct answer'
    );
  });

  it('should open hint when buzz-in timer expires after an incorrect answer', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });

    room.state.roomSettings.buzzInTimerDurationMillis = 1000;
    room.state.roomSettings.answerDurationMillis = 1000;
    room.state.roomSettings.showAnswerDurationMillis = 1000;

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Wrong Answer'
    });
    await room.waitForNextPatch();

    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, -200);

    // Wait for the buzz-in timer to expire for other players
    await delay(1100);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].hintOpened,
      1200
    );

    let clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.hintOpened,
      true,
      'Hint should be opened after timer expires'
    );

    // Wait for the second timer to expire to show the answer
    await delay(1100);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].showAnswer,
      1200
    );

    clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      clueState.showAnswer,
      true,
      'Answer should be shown after the second timeout'
    );
  });

  it('should not allow buzzing in after the timer has expired', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    const player2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    room.state.roomSettings.buzzInTimerDurationMillis = 1000;

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Wait for the buzz-in timer to expire
    await delay(1100);
    await room.clock.tick();
    await waitForCondition(
      () =>
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0].hintOpened,
      1200
    );

    const clueStateBefore = {
      ...room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0]
    };

    // Player 1 tries to buzz in after timer expired
    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await delay(100);
    await room.clock.tick();

    const clueStateAfter =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.deepStrictEqual(
      clueStateAfter.buzzedInPlayerQueue,
      clueStateBefore.buzzedInPlayerQueue,
      'Buzzer queue should not change'
    );

    // Player 2 tries to submit a guess directly
    player2.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Buzzer Answer'
    });
    await delay(100);
    await room.clock.tick();

    const player2Info = room.state.playerInfos.find(
      p => p.id === 'player2-uid'
    );
    assert.equal(player2Info.score, 0, 'Player 2 score should not change');
  });

  it('should penalize player and advance turn if answer timer expires in buzzer mode', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    const player2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    room.state.roomSettings.answerDurationMillis = 1000;

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Player 1 buzzes in, Player 2 buzzes in second
    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    player2.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Wait for player 1's answer timer to expire
    await delay(1100);
    await room.clock.tick();
    await room.waitForNextPatch();

    // Check that player 1 was penalized
    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, -200);

    // Check that it's now player 2's turn
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 1);
    assert.equal(clueState.buzzedInPlayerQueue[1], 'player2-uid');

    // Player 2 answers correctly
    player2.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Buzzer Answer'
    });
    await room.waitForNextPatch();

    const player2Info = room.state.playerInfos.find(
      p => p.id === 'player2-uid'
    );
    assert.equal(player2Info.score, 200);
  });

  it('should treat passing as an incorrect answer in buzzer mode', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER
    });

    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    const player2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    player1.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    player2.send('buzzIn', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Player 1 passes
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: '<PASSING>'
    });
    await room.waitForNextPatch();

    // Check that player 1 was penalized
    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, -200);

    // Check that it's now player 2's turn
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 1);
    assert.equal(clueState.buzzedInPlayerQueue[1], 'player2-uid');

    // Player 2 answers correctly
    player2.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Buzzer Answer'
    });
    await room.waitForNextPatch();

    const player2Info = room.state.playerInfos.find(
      p => p.id === 'player2-uid'
    );
    assert.equal(player2Info.score, 200);
  });
});

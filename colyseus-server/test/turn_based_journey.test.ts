import { ColyseusTestServer, boot } from '@colyseus/testing';
import { strict as assert } from 'assert';
import { JeopardyRoom } from '../src/rooms/JeopardyRoom';
import { GameMode, RoomSettings } from '../src/room_schema';
import appConfig from '../src/app.config';
import { LlmService } from '../src/llm_service';
import { FakeDatabase } from '../src/fake_database';
import { IAuth } from '../src/auth';
import { DecodedIdToken } from 'firebase-admin/auth';
import { delay, waitForCondition } from './test_helpers';
import { ServiceRegistry } from '../src/services';

class MockAuthService implements IAuth {
  private userMap = new Map<string, any>();

  constructor() {
    this.userMap.set('player2-uid', { displayName: 'Host', photoURL: '' });
    this.userMap.set('player1-uid', { displayName: 'Player 1', photoURL: '' });
  }

  verifyIdToken(token: string): Promise<DecodedIdToken> {
    const uid = token.replace('-token', '');
    return Promise.resolve({ uid } as DecodedIdToken);
  }

  getUser(uid: string): Promise<any> {
    return Promise.resolve(this.userMap.get(uid));
  }
}

class MockLlmServiceForTurnBased extends LlmService {
  override async generate(prompt: string): Promise<string> {
    const fakeApiResponse = [
      {
        categoryTitle: 'Turn-Based Category',
        questions: [
          { answer: 'Answer 2', value: 400, questionHTML: 'Question 2' },
          { answer: 'Answer 1', value: 200, questionHTML: 'Question 1' }
        ]
      }
    ];
    return '```json\n' + JSON.stringify(fakeApiResponse) + '\n```';
  }
}

describe('Turn-Based Mode Journey', () => {
  let colyseus: ColyseusTestServer;
  let db: FakeDatabase;

  beforeEach(async () => {
    db = new FakeDatabase();
    ServiceRegistry.initialize({
      database: db,
      auth: new MockAuthService(),
      llm: new MockLlmServiceForTurnBased()
    });
    colyseus = await boot(appConfig);
  });

  afterEach(async () => {
    if (colyseus) {
      await colyseus.shutdown();
    }
    ServiceRegistry.destroy();
  });

  it('should handle a full turn-based flow', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.TURN_BASED,
      llmService: new MockLlmServiceForTurnBased(),
      databaseService: db,
      authService: new MockAuthService()
    });

    const host = await colyseus.connectTo(room, { token: 'player2-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });

    // Start game
    host.send('startRoom');
    await room.waitForNextPatch(); // GeneratingQuestions
    await room.waitForNextPatch(); // RoomState
    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.equal(room.state.gameState.roomState.currentPlayerIdx, 0);

    // Host (Player 0) selects a clue
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Host answers correctly
    host.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Answer 1'
    });
    await room.waitForNextPatch();

    let hostInfo = room.state.playerInfos.find(p => p.id === 'player2-uid');
    assert.equal(hostInfo.score, 200); // Full points
    assert.equal(
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0].showAnswer,
      true
    );
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      1,
      'Turn should pass to Player 1'
    );
    assert.equal(
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0].clueComplete,
      false
    );
    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();
    assert.equal(
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0].clueComplete,
      true
    );

    // Player 1 selects a clue
    player1.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 1 });
    await room.waitForNextPatch();

    // Player 1 answers incorrectly
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 1,
      guess: 'Wrong Answer'
    });
    await room.waitForNextPatch();

    // Host (next in queue) answers correctly
    host.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 1,
      guess: 'Answer 2'
    });
    await room.waitForNextPatch();

    hostInfo = room.state.playerInfos.find(p => p.id === 'player2-uid');
    assert.equal(hostInfo.score, 600); // 200 + 400
    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();

    // After the clue is complete, the main turn should advance to the next player in the room's order
    const finalClueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[1];
    assert.equal(finalClueState.clueComplete, true);
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      0,
      'Turn should pass back to Host'
    );
  });

  it('should correctly cycle through players in turn-based mode', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.TURN_BASED,
      llmService: new MockLlmServiceForTurnBased(),
      databaseService: db,
      authService: new MockAuthService()
    });

    const host = await colyseus.connectTo(room, { token: 'player2-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });

    // Start game
    host.send('startRoom');
    await room.waitForNextPatch(); // GeneratingQuestions
    await room.waitForNextPatch(); // RoomState
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      0,
      'Host should start'
    );

    // Host (Player 0) selects a clue and answers correctly
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    host.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Answer 1'
    });
    await room.waitForNextPatch();
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      1,
      'Turn should pass to Player 1 after correct answer'
    );
    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();

    // Player 1 selects a clue and answers incorrectly
    player1.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 1 });
    await room.waitForNextPatch();
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 1,
      guess: 'Wrong Answer'
    });
    await room.waitForNextPatch();

    // Host (next in queue) also answers incorrectly
    host.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 1,
      guess: 'Wrong Answer 2'
    });
    await room.waitForNextPatch();

    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();
    // Clue is over, turn should pass to the next player in the main sequence (Host)
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      0,
      'Turn should pass back to Host after incorrect answers'
    );
  });

  it('should advance turn without penalty if answer timer expires in turn-based mode', async () => {
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.TURN_BASED,
      llmService: new MockLlmServiceForTurnBased(),
      databaseService: db,
      authService: new MockAuthService()
    });

    const host = await colyseus.connectTo(room, { token: 'player2-uid-token' });
    const player1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });

    room.state.roomSettings.answerDurationMillis = 1000;

    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    // Host selects a clue
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();

    // Wait for the host's answer timer to expire

    await delay(room.state.roomSettings.answerDurationMillis + 100);
    await room.clock.tick();
    await room.waitForNextPatch();

    // Check that the host was not penalized
    const hostInfo = room.state.playerInfos.find(p => p.id === 'player2-uid');
    assert.equal(hostInfo.score, 0);

    // Check that it's now player 1's turn
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 1);
    assert.equal(clueState.buzzedInPlayerQueue[1], 'player1-uid');

    // Player 1 answers correctly
    player1.send('submitGuess', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      guess: 'Answer 1'
    });
    await room.waitForNextPatch();

    const player1Info = room.state.playerInfos.find(
      p => p.id === 'player1-uid'
    );
    assert.equal(player1Info.score, 200);
  });
});

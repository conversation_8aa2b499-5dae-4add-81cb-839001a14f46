
import { strict as assert } from "assert";
import { GameMode, LlmAgent, PlayerInfo, FullRoomState, RoomStateProgress, RoundState, CategoryState, ClueState, RoomData, RoundData, Category, Clue, Room, RoomSettings } from "../src/room_schema";
import { ArraySchema } from "@colyseus/schema";
import { JeopardyRoomState } from "../src/rooms/schema/JeopardyRoomState";


describe("JeopardyRoomState Schema", () => {
    it("should correctly construct from a plain JSON object", () => {
        const mockRoomData:Room = {
            roomId: "test-room",
            host: "host-id",
            mode: GameMode.BUZZER,
            playerInfos: [
                { id: "player1", userName: "Player One", score: 100},
                { id: "player2", userName: "Player Two", score: 200}
            ],
            roomSettings: {
                gameMode: GameMode.BUZZER,
                llmAgent: { provider: "Gemini", modelName: "gemini-pro" }
            },
            gameState: {
                progressType: "RoomState",
                roomState: {
                    roundIdx: 0,
                    currentPlayerIdx: 1,
                    roundStates: [{
                        currentCategoryIdx: 0,
                        currentClueIdx: 0,
                        categoryStates: [{
                            clueStates: [
                                { clueComplete: true },
                                { clueComplete: false }
                            ]
                        }]
                    }]
                }
            },
            roomData: {
                rounds: [{
                    categories: [{
                        categoryTitle: "Test Category",
                        clues: [
                            { answer: "Answer 1", value: 100 },
                            { answer: "Answer 2", value: 200 }
                        ]
                    }]
                }]
            }
        };

        const roomState = new JeopardyRoomState(mockRoomData);

        // Assert top-level properties
        assert.equal(roomState.roomId, "test-room");
        assert.equal(roomState.host, "host-id");
        assert.equal(roomState.mode, GameMode.BUZZER);

        // Assert nested schema objects
        assert.ok(roomState.roomSettings instanceof RoomSettings);
        assert.equal(roomState.roomSettings.gameMode, GameMode.BUZZER);
        assert.ok(roomState.roomSettings.llmAgent instanceof LlmAgent);
        assert.equal(roomState.roomSettings.llmAgent.provider, "Gemini");

        // Assert arrays of schema objects
        assert.ok(roomState.playerInfos instanceof ArraySchema);
        assert.equal(roomState.playerInfos.length, 2);
        assert.ok(roomState.playerInfos[0] instanceof PlayerInfo);
        assert.equal(roomState.playerInfos[0].userName, "Player One");

        // Assert deeply nested properties
        assert.ok(roomState.gameState instanceof FullRoomState);
        assert.ok(roomState.gameState.roomState instanceof RoomStateProgress);
        assert.equal(roomState.gameState.roomState.roundStates[0].categoryStates[0].clueStates[0].clueComplete, true);
        
        assert.ok(roomState.roomData.rounds[0].categories[0].clues[1] instanceof Clue);
        assert.equal(roomState.roomData.rounds[0].categories[0].clues[1].answer, "Answer 2");
    });
});

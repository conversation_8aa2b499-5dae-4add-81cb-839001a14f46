import { Room, Client } from 'colyseus';
import { JeopardyRoom } from '../src/rooms/JeopardyRoom';
import { RoomSettings, GameMode, LlmAgent } from '../src/room_schema';
import { updateSettingsHandler } from '../src/handlers/update_settings_handler';
import { strict as assert } from 'assert';
import { JeopardyRoomState } from '../src/rooms/schema/JeopardyRoomState';
import { FakeDatabase } from '../src/fake_database';
import { ArraySchema } from '@colyseus/schema';
import { ServiceRegistry } from '../src/services';

// Mock the JeopardyRoom class
class MockJeopardyRoom extends Room<any> {
  state: JeopardyRoomState;
  databaseService = new FakeDatabase();

  constructor() {
    super();
    this.sessionIdToUidMap = new Map<string, string>();
    this.state = new JeopardyRoomState({
      host: 'host-session-id',
      roomSettings: new RoomSettings(),
      gameState: { progressType: 'WaitingRoom' },
      categoryTitles: new ArraySchema<string>('Initial Category')
    });
    // Initialize with default settings
    this.state.roomSettings.gameMode = GameMode.TURN_BASED;
    this.state.roomSettings.answerDurationMillis = 60000;

    this.sessionIdToUidMap.set('host-session-id', 'host-session-id');
    this.sessionIdToUidMap.set('other-session-id', 'other-session-id');
  }

  // Mock the 'this' context for the handler
  updateSettings = updateSettingsHandler.bind(this);
}

describe('updateSettingsHandler', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    hostClient = { sessionId: 'host-session-id' } as Client;
    otherClient = { sessionId: 'other-session-id' } as Client;
    ServiceRegistry.initialize({
      database: new FakeDatabase()
    });
  });

  it('should allow the host to update settings', () => {
    const newSettings: Partial<RoomSettings> = {
      gameMode: GameMode.BUZZER,
      answerDurationMillis: 30000
    };

    room.updateSettings(hostClient, newSettings);

    assert.equal(room.state.roomSettings.gameMode, GameMode.BUZZER);
    assert.equal(room.state.roomSettings.answerDurationMillis, 30000);
  });

  it('should update categoryTitles', () => {
    const newSettings = {
      categoryTitles: ['New Category 1', 'New Category 2']
    };

    room.updateSettings(hostClient, newSettings);

    assert.deepStrictEqual(Array.from(room.state.categoryTitles), [
      'New Category 1',
      'New Category 2'
    ]);
  });

  it('should update the llmAgent', () => {
    const newSettings = {
      llmAgent: {
        provider: 'Gemini',
        modelName: 'gemini-pro'
      }
    };

    room.updateSettings(hostClient, newSettings);

    assert.equal(room.state.roomSettings.llmAgent.provider, 'Gemini');
    assert.equal(room.state.roomSettings.llmAgent.modelName, 'gemini-pro');
  });

  it('should not allow a non-host client to update settings', () => {
    const initialSettings = { ...room.state.roomSettings };
    const newSettings: Partial<RoomSettings> = {
      gameMode: GameMode.BUZZER
    };

    room.updateSettings(otherClient, newSettings);

    assert.deepStrictEqual(
      JSON.stringify(room.state.roomSettings),
      JSON.stringify(initialSettings)
    );
  });

  it('should not update settings if the game has already started', () => {
    room.state.gameState.progressType = 'RoomState'; // Game is active
    const initialSettings = { ...room.state.roomSettings };
    const newSettings: Partial<RoomSettings> = {
      gameMode: GameMode.GAMEMASTER
    };

    room.updateSettings(hostClient, newSettings);

    assert.deepStrictEqual(
      JSON.stringify(room.state.roomSettings),
      JSON.stringify(initialSettings)
    );
  });

  it('should ignore properties that do not exist on RoomSettings', () => {
    const initialSettings = { ...room.state.roomSettings };
    const newSettings = {
      gameMode: GameMode.BUZZER,
      nonExistentProperty: 'some-value'
    };

    room.updateSettings(hostClient, newSettings as any);

    assert.equal(room.state.roomSettings.gameMode, GameMode.BUZZER);
    assert.strictEqual(
      (room.state.roomSettings as any).nonExistentProperty,
      undefined
    );
  });
});

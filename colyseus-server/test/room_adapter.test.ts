
import { adaptColyseusRoomToLegacy } from '../src/room_adapter';
import {
  Room as ColyseusRoomState,
  GameMode,
  PlayerInfo,
  RoomSettings,
  FullRoomState,
  WaitingRoom,
  GeneratingQuestions,
  RoomState as RoomStateProgress,
  RoundState,
  CategoryState,
  ClueState,
  RoomData,
  RoundData,
  Category,
  Clue,
} from '../src/room_schema';
import { Room as LegacyRoom, GeneratingQuestions as LegacyGeneratingQuestions } from '../../functions/src/resources';
import { Room } from 'colyseus.js';
import { deepStrictEqual } from 'assert';

class MockColyseusRoom extends Room<ColyseusRoomState> {
  private _state: ColyseusRoomState;
  public roomId: string;

  constructor(state: ColyseusRoomState) {
    super('test_room');
    this._state = state;
    this.roomId = state.roomId;
  }

  get state(): ColyseusRoomState {
    return this._state;
  }
}

describe('Room Adapter', () => {
  it('should adapt a basic room correctly', () => {
    const state = new ColyseusRoomState({
      roomId: 'test_room',
      host: 'host_id',
      mode: GameMode.TURN_BASED,
      createdAt: 1234567890,
    });
    const colyseusRoom = new MockColyseusRoom(state);

    const legacyRoom = adaptColyseusRoomToLegacy(colyseusRoom);

    deepStrictEqual(legacyRoom.roomId, 'test_room');
    deepStrictEqual(legacyRoom.host, 'host_id');
    deepStrictEqual(legacyRoom.mode, 1);
    deepStrictEqual(legacyRoom.createdAt, 1234567890);
    deepStrictEqual(legacyRoom.playerIds, []);
    deepStrictEqual(legacyRoom.playerInfos, []);
    deepStrictEqual(legacyRoom.categoryTitles, []);
  });

  it('should adapt a room with players', () => {
    const state = new ColyseusRoomState({
      roomId: 'test_room',
      host: 'host_id',
      playerIds: ['player1', 'player2'],
      playerInfos: [
        new PlayerInfo({ id: 'player1', userName: 'Player 1', score: 100 }),
        new PlayerInfo({ id: 'player2', userName: 'Player 2', score: 200 }),
      ],
    });
    const colyseusRoom = new MockColyseusRoom(state);

    const legacyRoom = adaptColyseusRoomToLegacy(colyseusRoom);

    deepStrictEqual(legacyRoom.playerIds, ['player1', 'player2']);
    deepStrictEqual(legacyRoom.playerInfos, [
      { score: 100, userName: 'Player 1', photoUrl: undefined },
      { score: 200, userName: 'Player 2', photoUrl: undefined },
    ]);
  });

  it('should adapt game state - WaitingRoom', () => {
    const state = new ColyseusRoomState({
      roomId: 'test_room',
      host: 'host_id',
      gameState: new FullRoomState({
        progressType: 'WaitingRoom',
        waitingRoom: new WaitingRoom(),
      }),
    });
    const colyseusRoom = new MockColyseusRoom(state);

    const legacyRoom = adaptColyseusRoomToLegacy(colyseusRoom);

    deepStrictEqual(legacyRoom.gameState.gameProgress.type, 'WaitingRoom');
  });

  it('should adapt game state - GeneratingQuestions', () => {
    const state = new ColyseusRoomState({
      roomId: 'test_room',
      host: 'host_id',
      gameState: new FullRoomState({
        progressType: 'GeneratingQuestions',
        generatingQuestions: new GeneratingQuestions({
          progress: 50,
          message: 'Generating...',
          category: 'History',
          questionIndex: 2,
        }),
      }),
    });
    const colyseusRoom = new MockColyseusRoom(state);

    const legacyRoom = adaptColyseusRoomToLegacy(colyseusRoom);
    const gameProgress = legacyRoom.gameState
      .gameProgress as LegacyGeneratingQuestions;

    deepStrictEqual(gameProgress.type, 'GeneratingQuestions');
    deepStrictEqual(gameProgress.progress, 50);
    deepStrictEqual(gameProgress.message, 'Generating...');
    deepStrictEqual(gameProgress.category, 'History');
    deepStrictEqual(gameProgress.questionIndex, 2);
  });

  it('should adapt room data with correct clue order', () => {
    const state = new ColyseusRoomState({
      roomId: 'test_room',
      host: 'host_id',
      roomData: new RoomData({
        rounds: [
          new RoundData({
            categories: [
              new Category({
                categoryTitle: 'Science',
                clues: [
                  new Clue({ value: 100, questionHTML: 'Q1' }),
                  new Clue({ value: 200, questionHTML: 'Q2' }),
                ],
              }),
              new Category({
                categoryTitle: 'History',
                clues: [
                  new Clue({ value: 100, questionHTML: 'H1' }),
                  new Clue({ value: 200, questionHTML: 'H2' }),
                ],
              }),
            ],
          }),
        ],
      }),
    });
    const colyseusRoom = new MockColyseusRoom(state);

    const legacyRoom = adaptColyseusRoomToLegacy(colyseusRoom);
    const round = legacyRoom.roomData.rounds[0];

    deepStrictEqual(round.categories[0].categoryTitle, 'Science');
    deepStrictEqual(round.categories[0].clues[0].value, 100);
    deepStrictEqual(round.categories[0].clues[0].questionHTML, 'Q1');
    deepStrictEqual(round.categories[0].clues[1].value, 200);
    deepStrictEqual(round.categories[0].clues[1].questionHTML, 'Q2');

    deepStrictEqual(round.categories[1].categoryTitle, 'History');
    deepStrictEqual(round.categories[1].clues[0].value, 100);
    deepStrictEqual(round.categories[1].clues[0].questionHTML, 'H1');
    deepStrictEqual(round.categories[1].clues[1].value, 200);
    deepStrictEqual(round.categories[1].clues[1].questionHTML, 'H2');
  });
});

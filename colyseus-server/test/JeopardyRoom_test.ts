import { expect } from 'chai';
import { Room, Client } from '@colyseus/core';
import { JeopardyRoom } from '../src/rooms/JeopardyRoom';
import { MockLlmService } from '../src/llm_service';
import { JeopardyRoomState } from '../src/rooms/schema/JeopardyRoomState';
import { GameMode } from '../src/room_schema';
import { ColyseusTestServer, boot } from '@colyseus/testing';
import appConfig from '../src/app.config';
import { IAuth } from '../src/auth';
import { DecodedIdToken } from 'firebase-admin/auth';
import { ServiceRegistry } from '../src/services';
import { FakeDatabase } from '../src/fake_database';
import { delay, FakeAuthService, waitForCondition } from './test_helpers';

class MockAuthService implements IAuth {
  private userMap = new Map<string, any>();

  constructor() {
    this.userMap.set('player1-uid', { displayName: 'Player 1', photoURL: '' });
    this.userMap.set('player2-uid', { displayName: 'Player 2', photoURL: '' });
  }

  verifyIdToken(token: string): Promise<DecodedIdToken> {
    const uid = token.replace('-token', '');
    return Promise.resolve({ uid } as DecodedIdToken);
  }

  getUser(uid: string): Promise<any> {
    return Promise.resolve(this.userMap.get(uid));
  }
}

class MockLlmServiceForTesting extends MockLlmService {
  async generate(prompt: string): Promise<string> {
    const fakeApiResponse = [
      {
        categoryTitle: 'Test Category',
        questions: [
          {
            answer: 'Test Answer',
            value: 100,
            questionHTML: '<div>This is a test question?</div>',
            hint: 'Test Hint',
            detailedFactsAboutAnswer: ["Because it's a test."]
          }
        ]
      }
    ];
    return '```json\n' + JSON.stringify(fakeApiResponse) + '\n```';
  }
}

describe('JeopardyRoom', () => {
  beforeEach(() => {
    ServiceRegistry.initialize({
      database: new FakeDatabase(),
      auth: new FakeAuthService(),
      llm: new MockLlmServiceForTesting()
    });
  });
  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should generate questions when the game starts', async () => {
    const colyseus = await boot(appConfig);
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.BUZZER,
      llmService: new MockLlmServiceForTesting(),
      authService: new MockAuthService()
    });

    const client1 = await colyseus.connectTo(room, {
      token: 'player1-uid-token'
    });
    const client2 = await colyseus.connectTo(room, {
      token: 'player2-uid-token'
    });

    client1.send('startRoom');

    await delay(1000);
    room.clock.tick();

    expect(room.state.gameState.progressType).to.equal('RoomState');
    expect(room.state.roomData.rounds.length).to.equal(1);
    expect(room.state.roomData.rounds[0].categories.length).to.equal(1);
    expect(room.state.roomData.rounds[0].categories[0].clues.length).to.equal(
      1
    );
    expect(
      room.state.roomData.rounds[0].categories[0].clues[0].questionHTML
    ).to.equal('<div>This is a mock question?</div>');

    await colyseus.shutdown();
  });
});

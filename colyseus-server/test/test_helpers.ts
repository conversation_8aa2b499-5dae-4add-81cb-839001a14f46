import { IAuth } from '@colyseus-server/auth';
import { DecodedIdToken } from 'firebase-admin/auth';

// Helper function to wait for a specific state condition
export async function waitForCondition(
  condition: () => boolean,
  timeout = 2000,
  pollInterval = 100
) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    if (condition()) {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, pollInterval));
  }
  throw new Error('Timeout waiting for condition.');
}

export const delay = (ms: number) =>
  new Promise(resolve => setTimeout(resolve, ms));

export class FakeAuthService implements IAuth {
  private userMap = new Map<string, any>();

  constructor() {
    this.userMap.set('host-uid', { displayName: 'Host', photoURL: '' });
    this.userMap.set('player1-uid', { displayName: 'Player 1', photoURL: '' });
    this.userMap.set('player2-uid', { displayName: 'Player 1', photoURL: '' });
  }

  verifyIdToken(token: string): Promise<DecodedIdToken> {
    const uid = token.replace('-token', '');
    return Promise.resolve({ uid } as DecodedIdToken);
  }

  getUser(uid: string): Promise<any> {
    return Promise.resolve(this.userMap.get(uid));
  }
}

import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { returnToBoardHandler } from '../../src/handlers/return_to_board_handler';
import { MockJeopardyRoom } from '../mock_room';
import {
  RoomSummary,
  RoomState,
  RoomStateProgress
} from '../../src/room_schema';

describe('returnToBoardHandler', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    hostClient = { sessionId: 'host-id' } as Client;
    otherClient = { sessionId: 'other-id' } as Client;

    room.sessionIdToUidMap.set(hostClient.sessionId, 'host-uid');
    room.sessionIdToUidMap.set(otherClient.sessionId, 'other-uid');
    room.state.host = 'host-uid';

    // Set up the initial state to be RoomSummary
    room.state.gameState.progressType = 'RoomSummary';
    const summary = new RoomSummary();
    const originalState = new RoomState();
    originalState.currentPlayerIdx = 1; // Some data to verify
    summary.originalState = originalState;
    room.state.gameState.roomSummary = summary;
  });

  it('should allow the host to return to the game board', () => {
    returnToBoardHandler.call(room, hostClient);

    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.ok(
      room.state.gameState.roomState,
      'RoomState should be restored'
    );
    assert.equal(
      room.state.gameState.roomState.currentPlayerIdx,
      1,
      'Restored state should have the correct data'
    );
    assert.strictEqual(
      room.state.gameState.roomSummary,
      undefined,
      'RoomSummary should be cleared'
    );
  });

  it('should not allow a non-host client to return to the game board', () => {
    returnToBoardHandler.call(room, otherClient);

    assert.equal(room.state.gameState.progressType, 'RoomSummary');
    assert.ok(
      room.state.gameState.roomSummary,
      'RoomSummary should still exist'
    );
    assert.strictEqual(
      room.state.gameState.roomState,
      undefined,
      'RoomState should not be restored'
    );
  });

  it('should not work if the game is not in the summary state', () => {
    room.state.gameState.progressType = 'RoomState';
    room.state.gameState.roomState = new RoomStateProgress();
    room.state.gameState.roomSummary = undefined;

    returnToBoardHandler.call(room, hostClient);

    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.ok(
      room.state.gameState.roomState,
      'RoomState should remain unchanged'
    );
  });
});

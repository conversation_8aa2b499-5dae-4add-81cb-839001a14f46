import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { sendChatMessage } from '../../src/handlers/send_chat_message_handler';
import { MockJeopardyRoom } from '../mock_room';
import { PlayerInfo } from '../../src/room_schema';

describe('sendChatMessage_handler', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    hostClient = { sessionId: 'host-id' } as Client;
    otherClient = { sessionId: 'other-id' } as Client;

    // Clear default players for this specific test
    room.state.playerInfos.clear();
    room.state.playerIds.clear();
    room.sessionIdToUidMap.clear();

    // Setup host
    room.sessionIdToUidMap.set(hostClient.sessionId, 'host-uid');
    room.state.playerInfos.push(
      new PlayerInfo({ id: 'host-uid', userName: 'Host Player' })
    );

    // Setup other player
    room.sessionIdToUidMap.set(otherClient.sessionId, 'other-uid');
    room.state.playerInfos.push(
      new PlayerInfo({ id: 'other-uid', userName: 'Other Player' })
    );
  });

  it('should broadcast a valid chat message', () => {
    const message = { content: 'Hello, world!' };
    sendChatMessage.call(room, hostClient, message);

    assert.equal(
      room.broadcastedMessages.length,
      1,
      'A message should be broadcast'
    );
    const broadcast = room.broadcastedMessages[0];
    assert.equal(broadcast.type, 'chat_message');
    assert.equal(broadcast.message.senderId, 'host-uid');
    assert.equal(broadcast.message.senderName, 'Host Player');
    assert.equal(broadcast.message.content, 'Hello, world!');
    assert.ok(broadcast.message.timestamp, 'Timestamp should be set');
  });

  it('should not broadcast a message from a client not in the room', () => {
    const unknownClient = { sessionId: 'unknown-id' } as Client;
    const message = { content: 'I am a ghost' };
    sendChatMessage.call(room, unknownClient, message);

    assert.equal(
      room.broadcastedMessages.length,
      0,
      'No message should be broadcast for an unknown client'
    );
  });

  it('should not broadcast an empty or whitespace message', () => {
    const emptyMessage = { content: '' };
    const whitespaceMessage = { content: '   ' };

    sendChatMessage.call(room, hostClient, emptyMessage);
    assert.equal(
      room.broadcastedMessages.length,
      0,
      'Empty message should not be broadcast'
    );

    sendChatMessage.call(room, hostClient, whitespaceMessage);
    assert.equal(
      room.broadcastedMessages.length,
      0,
      'Whitespace message should not be broadcast'
    );
  });

  it('should handle messages from different players', () => {
    const hostMessage = { content: 'Message from host' };
    const otherMessage = { content: 'Message from other player' };

    sendChatMessage.call(room, hostClient, hostMessage);
    sendChatMessage.call(room, otherClient, otherMessage);

    assert.equal(
      room.broadcastedMessages.length,
      2,
      'Two messages should be broadcast'
    );
    assert.equal(room.broadcastedMessages[0].message.senderName, 'Host Player');
    assert.equal(
      room.broadcastedMessages[1].message.senderName,
      'Other Player'
    );
  });
});

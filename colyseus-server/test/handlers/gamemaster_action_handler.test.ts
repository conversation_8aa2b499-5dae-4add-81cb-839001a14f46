import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { JeopardyRoom } from '../../src/rooms/JeopardyRoom';
import {
  GameMasterAction,
  gameMasterActionHandler
} from '../../src/handlers/gamemaster_action_handler';
import {
  GameMode,
  RoundState,
  CategoryState,
  ClueState,
  RoomStateProgress
} from '../../src/room_schema';
import { JeopardyRoomState } from '../../src/rooms/schema/JeopardyRoomState';

class MockJeopardyRoomForGMActions extends JeopardyRoom {
  constructor() {
    super();
    this.state = new JeopardyRoomState();
    this.sessionIdToUidMap = new Map<string, string>();
    this.state.host = 'host-session-id';
    this.state.mode = GameMode.GAMEMASTER;
    this.state.gameState.progressType = 'RoomState';
    this.state.gameState.roomState = new RoomStateProgress();

    // Set an active clue
    const roomState = this.state.gameState.roomState;
    roomState.roundIdx = 0;

    const roundState = new RoundState();
    roundState.currentCategoryIdx = 0;
    roundState.currentClueIdx = 0;

    const categoryState = new CategoryState();
    const clueState = new ClueState();
    categoryState.clueStates.push(clueState);
    roundState.categoryStates.push(categoryState);
    roomState.roundStates.push(roundState);

    this.sessionIdToUidMap.set('host-session-id', 'host-session-id');
    this.sessionIdToUidMap.set('other-session-id', 'other-session-id');
  }

  gameMasterAction = gameMasterActionHandler.bind(this);
}

describe('gameMasterActionHandler', () => {
  let room: MockJeopardyRoomForGMActions;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoomForGMActions();
    hostClient = { sessionId: 'host-session-id' } as Client;
    otherClient = { sessionId: 'other-session-id' } as Client;
  });

  it('should allow the host to toggle the hint', () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.strictEqual(clueState.hintOpened, false);

    room.gameMasterAction(hostClient, { action: GameMasterAction.TOGGLE_HINT });
    assert.strictEqual(clueState.hintOpened, true);

    room.gameMasterAction(hostClient, { action: GameMasterAction.TOGGLE_HINT });
    assert.strictEqual(clueState.hintOpened, false);
  });

  it('should allow the host to toggle the answer and mark clue as complete', () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.strictEqual(clueState.showAnswer, false);
    assert.strictEqual(clueState.clueComplete, false);

    room.gameMasterAction(hostClient, {
      action: GameMasterAction.TOGGLE_ANSWER
    });
    assert.strictEqual(clueState.showAnswer, true);
    assert.strictEqual(clueState.clueComplete, true);
  });

  it('should not allow a non-host to perform actions', () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];

    room.gameMasterAction(otherClient, {
      action: GameMasterAction.TOGGLE_HINT
    });
    assert.strictEqual(clueState.hintOpened, false);
  });

  it('should do nothing if no clue is active', () => {
    room.state.gameState.roomState.roundStates[0].currentClueIdx = -1;
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];

    room.gameMasterAction(hostClient, { action: GameMasterAction.TOGGLE_HINT });
    assert.strictEqual(clueState.hintOpened, false);
  });
});

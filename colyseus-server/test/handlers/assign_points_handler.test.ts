import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { Je<PERSON><PERSON>yRoom } from '../../src/rooms/JeopardyRoom';
import { assignPointsHandler } from '../../src/handlers/assign_points_handler';
import {
  GameMode,
  PlayerInfo,
  RoomStateProgress,
  RoundState,
  CategoryState,
  ClueState,
  ArraySchema,
  RoundData,
  Category,
  Clue
} from '../../src/room_schema';
import { JeopardyRoomState } from '../../src/rooms/schema/JeopardyRoomState';
import { FakeDatabase } from '../../src/fake_database';
import { ServiceRegistry } from '../../src/services';
import { delay } from '../test_helpers';

class MockJeopardyRoomForAssignPoints extends JeopardyRoom {
  constructor() {
    super();
    this.state = new JeopardyRoomState();
    this.sessionIdToUidMap = new Map<string, string>();
    this.state.host = 'host-session-id';
    this.state.mode = GameMode.GAMEMASTER;
    this.state.roomSettings.gameMode = GameMode.GAMEMASTER;
    this.state.playerIds.push('player1-id', 'player2-id');
    this.state.playerInfos.push(
      new PlayerInfo({ id: 'player1-id', userName: 'Player 1', score: 100 })
    );
    this.state.playerInfos.push(
      new PlayerInfo({ id: 'player2-id', userName: 'Player 2', score: 200 })
    );
    this.state.gameState.progressType = 'RoomState';
    this.state.gameState.roomState = new RoomStateProgress();

    this.sessionIdToUidMap.set('host-session-id', 'host-session-id');

    const rd = new RoundData();
    const cat = new Category();
    const clue = new Clue();
    clue.value = 100;
    cat.clues.push(clue);
    rd.categories.push(cat);
    this.state.roomData.rounds.push(rd);

    // Set an active clue
    const roomState = this.state.gameState.roomState;
    roomState.roundIdx = 0;
    const roundState = new RoundState();
    roundState.currentCategoryIdx = 0;
    roundState.currentClueIdx = 0;
    const categoryState = new CategoryState();
    const cs = new ClueState();
    categoryState.clueStates.push(cs);
    roundState.categoryStates.push(categoryState);
    roomState.roundStates.push(roundState);
  }

  assignPoints = assignPointsHandler.bind(this);
}

describe('assignPointsHandler', () => {
  let room: MockJeopardyRoomForAssignPoints;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoomForAssignPoints();
    hostClient = { sessionId: 'host-session-id' } as Client;
    otherClient = { sessionId: 'other-session-id' } as Client;
    ServiceRegistry.initialize({
      database: new FakeDatabase()
    });
  });

  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should allow the host to assign points to a player', async () => {
    const player = room.state.playerInfos.find(p => p.id === 'player1-id');
    assert.strictEqual(player?.score, 100);

    room.assignPoints(hostClient, { team: 'Player 1' });
    assert.strictEqual(player.score, 200);
    const clueState =
      room.state.gameState.roomState?.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.strictEqual(clueState?.showAnswer, true);
    assert.strictEqual(clueState?.clueComplete, false);
    assert.strictEqual(clueState?.answeredByPlayerId, 'Player 1');

    await delay(room.state.roomSettings.showAnswerDurationMillis + 10);
    room.clock.tick();
    assert.strictEqual(clueState.clueComplete, true);

    // Verify state was saved
    const savedState = await ServiceRegistry.getInstance().database.loadRoom(
      room.state.roomId
    );
    assert.ok(savedState);
    assert.equal(
      savedState.playerInfos.find(p => p.id === 'player1-id').score,
      200
    );
  });

  it('should not allow a non-host to assign points', () => {
    const player = room.state.playerInfos.find(p => p.id === 'player1-id');

    room.assignPoints(otherClient, { team: 'Player 1' });
    assert.strictEqual(player.score, 100);
  });

  it('should do nothing if the player ID is not found', () => {
    const player = room.state.playerInfos.find(p => p.id === 'player1-id');

    room.assignPoints(hostClient, { team: 'Player 5' });
    assert.strictEqual(player.score, 100);
  });
});

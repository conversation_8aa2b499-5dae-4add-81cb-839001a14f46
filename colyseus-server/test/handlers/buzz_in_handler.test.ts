import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { MockJeopardyRoom } from '../mock_room';
import {
  GameMode,
  RoomStateProgress,
  RoundState,
  CategoryState,
  ClueState
} from '../../src/room_schema';
import {
  buzzInHandler,
  BuzzInPayload
} from '../../src/handlers/buzz_in_handler';

describe('buzzInHandler', () => {
  let room: MockJeopardyRoom;
  let player1: Client;
  let player2: Client;
  let payload: BuzzInPayload;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    room.setGameMode(GameMode.BUZZER);

    player1 = { sessionId: 'player1-id' } as Client;
    player2 = { sessionId: 'player2-id' } as Client;

    // Setup active clue state
    room.state.gameState.progressType = 'RoomState';
    const roomState = new RoomStateProgress();
    const roundState = new RoundState();
    roundState.currentCategoryIdx = 0;
    roundState.currentClueIdx = 0;
    const categoryState = new CategoryState();
    const clueState = new ClueState();
    clueState.buzzInTimerStartTime = room.clock.currentTime;
    categoryState.clueStates.push(clueState);
    roundState.categoryStates.push(categoryState);
    roomState.roundStates.push(roundState);
    room.state.gameState.roomState = roomState;

    room.state.roomSettings.buzzInTimerDurationMillis = 500; // .5 seconds

    payload = { roundIdx: 0, categoryIdx: 0, clueIdx: 0 };
  });

  it('should add a player to the buzz-in queue', () => {
    buzzInHandler.call(room, player1, payload);
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.buzzedInPlayerQueue.length, 1);
    assert.equal(clueState.buzzedInPlayerQueue[0], player1.sessionId);
  });

  it('should set the answer timer for the first player', () => {
    buzzInHandler.call(room, player1, payload);
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 0);
    assert.ok(clueState.answerStartTime > 0);
  });

  it('should not allow a player to buzz in twice', () => {
    buzzInHandler.call(room, player1, payload);
    buzzInHandler.call(room, player1, payload); // Second attempt
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.buzzedInPlayerQueue.length, 1);
  });

  it('should add multiple players to the queue in order', () => {
    buzzInHandler.call(room, player1, payload);
    buzzInHandler.call(room, player2, payload);
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.buzzedInPlayerQueue.length, 2);
    assert.equal(clueState.buzzedInPlayerQueue[0], player1.sessionId);
    assert.equal(clueState.buzzedInPlayerQueue[1], player2.sessionId);
  });

  it('should not allow buzzing in after the timer expires', async () => {
    room.clock.setTimeout(async () => {
      await room.clock.tick(501);
      buzzInHandler.call(room, player1, payload);
      const clueState =
        room.state.gameState.roomState.roundStates[0].categoryStates[0]
          .clueStates[0];
      assert.equal(clueState.buzzedInPlayerQueue.length, 0);
    }, 501);
  });
});

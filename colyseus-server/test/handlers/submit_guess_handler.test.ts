import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { MockJeopardyRoom } from '../mock_room';
import { delay } from '../test_helpers';
import {
  GameMode,
  RoomStateProgress,
  RoundState,
  CategoryState,
  ClueState,
  Clue,
  RoundData,
  Category
} from '../../src/room_schema';
import {
  submitGuessHandler,
  SubmitGuessPayload
} from '../../src/handlers/submit_guess_handler';
import { startHintPhase } from '../../src/handlers/clue_lifecycle_helpers';
import { ServiceRegistry } from '../../src/services';
import { selectClueHandler } from '../../src/handlers/select_clue_handler';
import { FakeDatabase } from '../../src/fake_database';
import { LlmService } from '../../src/llm_service';

class MockLlmVerifier extends LlmService {
  override async generate(prompt: string): Promise<string> {
    return '```json\n' + JSON.stringify({ isCorrect: false, explanation: '' }) + '\n```';
  }
};

describe('submitGuessHandler', () => {
  let room: MockJeopardyRoom;
  let player1: Client;
  let player2: Client;
  let payload: SubmitGuessPayload;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    room.setGameMode(GameMode.BUZZER);

    player1 = { sessionId: 'player1-id' } as Client;
    player2 = { sessionId: 'player2-id' } as Client;

    // Setup active clue state
    room.state.gameState.progressType = 'RoomState';
    const roomState = new RoomStateProgress();
    const roundState = new RoundState();
    const categoryState = new CategoryState();
    const clueState = new ClueState();
    categoryState.clueStates.push(clueState);
    roundState.categoryStates.push(categoryState);
    roomState.roundStates.push(roundState);
    room.state.gameState.roomState = roomState;
    room.state.roomSettings.gameMode = GameMode.BUZZER;
    selectClueHandler.call(room, player1, {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0
    });

    clueState.buzzedInPlayerQueue.push(player1.sessionId, player2.sessionId);
    clueState.queueAnswerTurnIdx = 0; // Player 1's turn
    // Add clue data
    const roundData = new RoundData();
    const category = new Category();
    const clueData = new Clue();
    clueData.answer = 'Correct Answer';
    clueData.value = 400;
    category.clues.push(clueData);
    roundData.categories.push(category);
    room.state.roomData.rounds.push(roundData);

    payload = { roundIdx: 0, categoryIdx: 0, clueIdx: 0, guess: '' };
    ServiceRegistry.initialize({ database: new FakeDatabase(), llm: new MockLlmVerifier() });
  });

  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should award points for a correct answer', async () => {
    payload.guess = 'Correct Answer';
    await submitGuessHandler.call(room, player1, payload);
    const player1Info = room.state.playerInfos.find(
      p => p.id === player1.sessionId
    );
    assert.equal(player1Info.score, 400);
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
        await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();
      assert.equal(clueState.clueComplete, true);
      assert.equal(clueState.showAnswer, true);
  });

  it('should pass the turn to the next player on an incorrect answer', async() => {
    payload.guess = 'Wrong Answer';
    await submitGuessHandler.call(room, player1, payload);

    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 1); // Should be player 2's turn
    assert.ok(clueState.answerStartTime > 0);
    assert.equal(clueState.clueComplete, false);
  });

  it('should progress to next state if all players answer incorrectly', async () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.hintOpened, false);
    payload.guess = 'Wrong Answer 1';
    await submitGuessHandler.call(room, player1, payload);
    assert.equal(clueState.hintOpened, false);

    payload.guess = 'Wrong Answer 2';
    await submitGuessHandler.call(room, player2, payload);

    assert.equal(clueState.hintOpened, true);
    assert.equal(clueState.clueComplete, false);
    assert.equal(clueState.showAnswer, false);
    clueState.buzzedInPlayerQueue.push(player1.sessionId, player2.sessionId);
    clueState.queueAnswerTurnIdx = 0; // Player 1's turn

    payload.guess = 'Wrong Answer 1';
    await submitGuessHandler.call(room, player1, payload);
    payload.guess = 'Wrong Answer 2';
    await submitGuessHandler.call(room, player2, payload);
    await delay(room.state.roomSettings.buzzInTimerDurationMillis + 100);
    room.clock.tick();
    assert.equal(clueState.showAnswer, true);

    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();
    assert.equal(clueState.clueComplete, true);
  });
  it('should progress to next state if a player answers incorrectly and timer expires', async () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    // Unbuzz player 2.
    clueState.buzzedInPlayerQueue.splice(1, 1);
    assert.equal(clueState.hintOpened, false);
    payload.guess = 'Wrong Answer 1';
    await submitGuessHandler.call(room, player1, payload);
    assert.equal(clueState.hintOpened, false);
    await delay(room.state.roomSettings.buzzInTimerDurationMillis + 100);
    room.clock.tick();
    assert.equal(clueState.hintOpened, true);
    assert.equal(clueState.clueComplete, false);
    assert.equal(clueState.showAnswer, false);
    clueState.buzzedInPlayerQueue.push(player1.sessionId);
    clueState.queueAnswerTurnIdx = 0; // Player 1's turn

    payload.guess = 'Wrong Answer 1';
    await submitGuessHandler.call(room, player1, payload);
    await delay(room.state.roomSettings.buzzInTimerDurationMillis + 100);
    room.clock.tick();
    assert.equal(clueState.showAnswer, true);

    await delay(room.state.roomSettings.showAnswerDurationMillis + 100);
    room.clock.tick();
    assert.equal(clueState.clueComplete, true);
  });

  it('should not allow a player to answer out of turn', () => {
    payload.guess = 'Correct Answer';
    submitGuessHandler.call(room, player2, payload); // Player 2 tries to answer on Player 1's turn

    const player2Info = room.state.playerInfos.find(
      p => p.id === player2.sessionId
    );
    assert.equal(player2Info.score, 0); // Score should not change
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(clueState.queueAnswerTurnIdx, 0); // Turn should not change
  });

  it('should award half points if the hint was opened', async () => {
    const clueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    clueState.hintOpened = true;

    payload.guess = 'Correct Answer';
    await submitGuessHandler.call(room, player1, payload);

    const player1Info = room.state.playerInfos.find(
      p => p.id === player1.sessionId
    );
    assert.equal(player1Info.score, 200); // Half of 400
  });
});

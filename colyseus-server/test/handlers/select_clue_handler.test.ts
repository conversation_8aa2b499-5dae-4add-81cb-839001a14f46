
import { strict as assert } from "assert";
import { Client } from "colyseus";
import { <PERSON>opardyRoom } from "../../src/rooms/JeopardyRoom";
import { select<PERSON><PERSON><PERSON>and<PERSON> } from "../../src/handlers/select_clue_handler";
import { GameMode, RoundState, CategoryState, ClueState, PlayerInfo, RoomStateProgress } from "../../src/room_schema";
import { JeopardyRoomState } from "../../src/rooms/schema/JeopardyRoomState";

class MockJeopardyRoomForClueSelection extends JeopardyRoom {
    constructor(mode: GameMode) {
        super();
        this.state = new JeopardyRoomState();
        this.sessionIdToUidMap = new Map<string, string>();
        this.state.host = "host-session-id";
        this.state.mode = mode;
        this.state.gameState.progressType = "RoomState";
        this.state.gameState.roomState = new RoomStateProgress();

        // Add players
        this.state.playerIds.push("player1-id", "player2-id", "host-session-id");
        this.state.playerInfos.push(new PlayerInfo({ id: "player1-id", userName: "Player 1" }));
        this.state.playerInfos.push(new PlayerInfo({ id: "player2-id", userName: "Player 2" }));
        this.state.playerInfos.push(new PlayerInfo({ id: "host-session-id", userName: "Host" }));
        
        this.sessionIdToUidMap.set("player1-id", "player1-id");
        this.sessionIdToUidMap.set("player2-id", "player2-id");
        this.sessionIdToUidMap.set("host-session-id", "host-session-id");

        // Set current player for turn-based
        if (mode === GameMode.TURN_BASED) {
            this.state.gameState.roomState.currentPlayerIdx = 0; // Player 1's turn
        }

        // Initialize game state with one clue
        const roundState = new RoundState();
        roundState.currentClueIdx = -1;
        const categoryState = new CategoryState();
        const clueState = new ClueState();
        categoryState.clueStates.push(clueState);
        roundState.categoryStates.push(categoryState);
        this.state.gameState.roomState.roundStates.push(roundState);
    }

    selectClue = selectClueHandler.bind(this);
}

describe("selectClueHandler", () => {
    let player1: Client;
    let player2: Client;

    beforeEach(() => {
        player1 = { sessionId: "player1-id" } as Client;
        player2 = { sessionId: "player2-id" } as Client;
    });

    it("should allow the current player to select a clue in TURN_BASED mode", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.TURN_BASED);
        room.selectClue(player1, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });

        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, 0);
        assert.ok(room.state.gameState.roomState.roundStates[0].categoryStates[0].clueStates[0].buzzInTimerStartTime > 0);
        assert.equal(room.state.gameState.roomState.roundStates[0].categoryStates[0].clueStates[0].buzzedInPlayerQueue.length, 3);
        assert.equal(room.state.gameState.roomState.roundStates[0].categoryStates[0].clueStates[0].buzzedInPlayerQueue[0], "player1-id");
    });

    it("should NOT allow a non-current player to select a clue in TURN_BASED mode", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.TURN_BASED);
        room.selectClue(player2, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });

        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, -1);
    });

    it("should allow any player to select a clue in BUZZER mode", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.BUZZER);
        room.selectClue(player2, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });

        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, 0);
    });
    
    it("should only allow the host to select a clue in GAMEMASTER mode", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.GAMEMASTER);
        const hostClient = { sessionId: "host-session-id" } as Client;
        
        // Non-host fails
        room.selectClue(player1, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, -1);

        // Host succeeds
        room.selectClue(hostClient, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, 0);
    });

    it("should not allow selecting a clue if one is already active", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.BUZZER);
        room.state.gameState.roomState.roundStates[0].currentClueIdx = 0; // A clue is already active

        room.selectClue(player1, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });

        // No change should have occurred
        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, 0);
    });

    it("should not allow selecting a completed clue", () => {
        const room = new MockJeopardyRoomForClueSelection(GameMode.BUZZER);
        room.state.gameState.roomState.roundStates[0].categoryStates[0].clueStates[0].clueComplete = true;

        room.selectClue(player1, { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });

        assert.equal(room.state.gameState.roomState.roundStates[0].currentClueIdx, -1);
    });
});

import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { Jeop<PERSON>yRoom } from '../../src/rooms/JeopardyRoom';
import { startGenerationHandler } from '../../src/handlers/start_generation_handler';
import { MockLlmService } from '../../src/llm_service';
import { FakeDatabase } from '../../src/fake_database';
import { GameMode, RoomSettings, WaitingRoom } from '../../src/room_schema';
import { JeopardyRoomState } from '../../src/rooms/schema/JeopardyRoomState';
import { ServiceRegistry } from '../../src/services';
import { FakeAuthService } from '../test_helpers';

class MockLlmServiceForGeneration extends MockLlmService {
  override async *generateStream(
    prompt: string
  ): AsyncGenerator<string, void, unknown> {
    const fakeApiResponse =
      '```json\n' +
      JSON.stringify([
        {
          categoryTitle: 'Test Category',
          questions: [
            {
              answer: 'Test Answer',
              value: 100,
              questionHTML: '<div>This is a test question?</div>',
              hint: 'Test Hint',
              detailedFactsAboutAnswer: ["Because it's a test."]
            }
          ]
        }
      ]) +
      '\n```';

    for (let i = 0; i < fakeApiResponse.length; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 10));
      yield fakeApiResponse.substring(i, i + 10);
    }
  }
}

class MockJeopardyRoomForGeneration extends JeopardyRoom {
  constructor() {
    super();
    this.state = new JeopardyRoomState();
    this.sessionIdToUidMap.set('host-session-id', 'host-session-id');
    this.state.host = 'host-session-id';
    this.state.gameState.progressType = 'WaitingRoom';
    this.state.gameState.waitingRoom = new WaitingRoom();
    this.state.roomSettings = new RoomSettings();
    this.state.categoryTitles.push('Test Category');
  }

  // Bind the handler to the mock room instance
  startGeneration = startGenerationHandler.bind(this);
}

describe('startGenerationHandler', () => {
  let room: MockJeopardyRoomForGeneration;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    ServiceRegistry.initialize({
      llm: new MockLlmServiceForGeneration(),
      database: new FakeDatabase(),
      auth: new FakeAuthService()
    });
    room = new MockJeopardyRoomForGeneration();
    hostClient = { sessionId: 'host-session-id' } as Client;
    otherClient = { sessionId: 'other-session-id' } as Client;
  });

  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should allow the host to start the game and generate questions', async () => {
    await room.startGeneration(hostClient);

    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.equal(room.state.roomData.rounds.length, 1);
    assert.equal(room.state.roomData.rounds[0].categories.length, 1);
    assert.equal(room.state.roomData.rounds[0].categories[0].clues.length, 1);

    // Verify that the room was saved
    const savedRoom = await ServiceRegistry.getInstance().database.loadRoom(
      room.state.roomId
    );
    assert.ok(savedRoom, 'Room should have been saved to the database');
    assert.equal(savedRoom.gameState.progressType, 'RoomState');
  });

  it('should not allow a non-host client to start the game', async () => {
    await room.startGeneration(otherClient);

    assert.equal(room.state.gameState.progressType, 'WaitingRoom');
    const savedRoom = await ServiceRegistry.getInstance().database.loadRoom(
      room.state.roomId
    );
    assert.strictEqual(savedRoom, null, 'Room should not have been saved');
  });

  it("should not start the game if it's not in the WaitingRoom state", async () => {
    room.state.gameState.progressType = 'RoomState'; // Game already active

    await room.startGeneration(hostClient);

    // State should remain unchanged
    assert.equal(room.state.gameState.progressType, 'RoomState');
    const savedRoom = await ServiceRegistry.getInstance().database.loadRoom(
      room.state.roomId
    );
    assert.strictEqual(savedRoom, null, 'Room should not have been saved');
  });

  it('should revert to WaitingRoom state if question generation fails', async () => {
    // Force the LLM service to throw an error
    ServiceRegistry.getInstance().llm.generateStream = async function*() {
      throw new Error('LLM failed');
    };

    await room.startGeneration(hostClient);

    assert.equal(room.state.gameState.progressType, 'WaitingRoom');
    const savedRoom = await ServiceRegistry.getInstance().database.loadRoom(
      room.state.roomId
    );
    assert.strictEqual(savedRoom, null, 'Room should not have been saved');
  });

  it('should call the progress callback with streamed chunks', async () => {
    const broadcastChunks: string[] = [];

    // Mock the broadcast method to capture chunks
    room.broadcast = (type: string, message: any) => {
      if (type === 'generationChunk') {
        broadcastChunks.push(message.chunk);
      }
    };

    // Call the handler and wait for it to complete
    await room.startGeneration(hostClient);

    // Assertions
    assert.ok(
      broadcastChunks.length > 1,
      'Should have broadcasted multiple chunks'
    );
  });
});

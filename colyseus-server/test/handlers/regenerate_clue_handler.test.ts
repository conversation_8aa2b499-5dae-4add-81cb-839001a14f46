import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { MockJeopardyRoom } from '../mock_room';
import {
  GameMode,
  RoundState,
  CategoryState,
  ClueState,
  RoomStateProgress,
  RoomData,
  RoundData,
  Category,
  Clue
} from '../../src/room_schema';
import {
  regenerateClueHandler,
  RegenerateCluePayload
} from '../../src/handlers/regenerate_clue_handler';
import { LlmService } from '../../src/llm_service';
import { ServiceRegistry } from '../../src/services';

class MockLlmServiceForRegen extends LlmService {
  override async generate(prompt: string): Promise<string> {
    const newClue = {
      questionHTML: '<div>This is a regenerated question?</div>',
      answer: 'Regenerated Answer',
      hint: 'Regenerated Hint',
      detailedFactsAboutAnswer: ['Regenerated Fact 1']
    };
    return '```json\n' + JSON.stringify(newClue) + '\n```';
  }
}

describe('regenerateClueHandler', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    ServiceRegistry.initialize({
      llm: new MockLlmServiceForRegen()
    });
    hostClient = { sessionId: 'player1-id' } as Client;

    room.state.host = hostClient.sessionId;
    room.setGameMode(GameMode.GAMEMASTER);

    // Mock a fully generated game state
    room.state.gameState.progressType = 'RoomState';
    room.state.gameState.roomState = new RoomStateProgress();

    const roundData = new RoundData();
    const category = new Category();
    const oldClue = new Clue();
    oldClue.questionHTML = 'Old Question';
    oldClue.answer = 'Old Answer';
    category.clues.push(oldClue);
    roundData.categories.push(category);
    room.state.roomData.rounds.push(roundData);

    const roundState = new RoundState();
    const categoryState = new CategoryState();
    const clueState = new ClueState({ clueComplete: true }); // Mark as complete to check for reset
    categoryState.clueStates.push(clueState);
    roundState.categoryStates.push(categoryState);
    room.state.gameState.roomState.roundStates.push(roundState);
  });

  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should allow the host to regenerate a clue', async () => {
    const payload: RegenerateCluePayload = {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0
    };
    await regenerateClueHandler.call(room, hostClient, payload);

    const newClue = room.state.roomData.rounds[0].categories[0].clues[0];
    assert.equal(
      newClue.questionHTML,
      '<div>This is a regenerated question?</div>'
    );
    assert.equal(newClue.answer, 'Regenerated Answer');

    const newClueState =
      room.state.gameState.roomState.roundStates[0].categoryStates[0]
        .clueStates[0];
    assert.equal(
      newClueState.clueComplete,
      false,
      'Clue state should be reset'
    );
  });

  it('should not allow a non-host to regenerate a clue', async () => {
    const otherClient = { sessionId: 'other-player' } as Client;
    const payload: RegenerateCluePayload = {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0
    };
    await regenerateClueHandler.call(room, otherClient, payload);

    const clue = room.state.roomData.rounds[0].categories[0].clues[0];
    assert.equal(clue.questionHTML, 'Old Question'); // Should not have changed
  });
});

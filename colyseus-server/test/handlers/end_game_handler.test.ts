import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { endGameHandler } from '../../src/handlers/end_game_handler';
import { MockJeopardyRoom } from '../mock_room';
import {
  RoomStateProgress,
  RoundState,
  CategoryState,
  ClueState,
  RoomData,
  RoundData,
  Category,
  Clue
} from '../../src/room_schema';

describe('endGameHandler', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;
  let otherClient: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    hostClient = { sessionId: 'player1-id' } as Client;
    otherClient = { sessionId: 'player2-id' } as Client;

    room.sessionIdToUidMap.set(hostClient.sessionId, 'host-uid');
    room.state.host = 'host-uid';
    room.state.gameState.progressType = 'RoomState';
    room.state.gameState.roomState = new RoomStateProgress();

    // Populate with some data to ensure it's copied correctly
    const roundState = new RoundState();
    const categoryState = new CategoryState();
    categoryState.clueStates.push(new ClueState());
    roundState.categoryStates.push(categoryState);
    room.state.gameState.roomState.roundStates.push(roundState);
    room.state.gameState.roomState.currentPlayerIdx = 1;
  });

  it('should allow the host to end the game', () => {
    endGameHandler.call(room, hostClient);
    assert.equal(room.state.gameState.progressType, 'RoomSummary');
    assert.ok(
      room.state.gameState.roomSummary,
      'RoomSummary state should be created'
    );
  });

  it('should not allow a non-host client to end the game', () => {
    endGameHandler.call(room, otherClient);
    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.strictEqual(
      room.state.gameState.roomSummary,
      undefined,
      'RoomSummary should not be created'
    );
  });

  it("should not end the game if it's not in progress", () => {
    room.state.gameState.progressType = 'WaitingRoom';
    endGameHandler.call(room, hostClient);
    assert.equal(room.state.gameState.progressType, 'WaitingRoom');
  });

  it('should correctly copy the final game state to the summary', () => {
    endGameHandler.call(room, hostClient);

    assert.equal(room.state.gameState.progressType, 'RoomSummary');
    const summary = room.state.gameState.roomSummary;
    assert.ok(summary, 'RoomSummary state should exist');

    const originalState = summary.originalState;
    assert.ok(originalState, 'Original state should be copied to summary');
    assert.equal(
      originalState.currentPlayerIdx,
      1,
      'currentPlayerIdx should be copied'
    );
    assert.equal(
      originalState.roundStates.length,
      1,
      'roundStates should be copied'
    );
    assert.equal(
      originalState.roundStates[0].categoryStates[0].clueStates.length,
      1,
      'clueStates should be copied'
    );
  });

  it('should clear the old roomState after transitioning', () => {
    endGameHandler.call(room, hostClient);
    assert.strictEqual(
      room.state.gameState.roomState,
      undefined,
      'Old roomState should be cleared'
    );
  });

  it('should generate memorable questions from completed clues', () => {
    // 1. Setup more detailed state
    room.state.gameState.roomState.roundStates.clear(); // Clear default state
    const completedClueState = new ClueState({ clueComplete: true });
    const incompleteClueState = new ClueState({ clueComplete: false });

    const categoryState = new CategoryState();
    categoryState.clueStates.push(completedClueState);
    categoryState.clueStates.push(incompleteClueState);

    const roundState = new RoundState();
    roundState.categoryStates.push(categoryState);
    room.state.gameState.roomState.roundStates.push(roundState);

    // 2. Setup corresponding roomData
    const completedClueData = new Clue({
      questionHTML: 'This is a test question',
      answer: 'Test Answer',
      value: 400
    });
    const categoryData = new Category({
      categoryTitle: 'Test Category'
    });
    categoryData.clues.push(completedClueData);
    categoryData.clues.push(new Clue()); // For the incomplete clue

    const roundData = new RoundData();
    roundData.categories.push(categoryData);
    room.state.roomData.rounds.push(roundData);

    // 3. Call the handler
    endGameHandler.call(room, hostClient);

    // 4. Assert the results
    assert.equal(room.state.gameState.progressType, 'RoomSummary');
    const summary = room.state.gameState.roomSummary;
    assert.ok(summary, 'RoomSummary should be created');

    const memorable = summary.memorableQuestions;
    assert.equal(
      memorable.length,
      1,
      'Should generate one memorable question'
    );

    const question = memorable[0];
    assert.equal(question.category, 'Test Category');
    assert.equal(question.question, 'This is a test question');
    assert.equal(question.answer, 'Test Answer');
    assert.equal(question.value, 400);
    assert.equal(
      question.reason,
      'This was one of the memorable questions from the game.'
    );
  });
});

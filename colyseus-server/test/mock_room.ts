import { Room } from 'colyseus';
import { JeopardyRoomState } from '../src/rooms/schema/JeopardyRoomState';
import { GameMode, PlayerInfo, RoomSettings } from '../src/room_schema';
import { IDatabase } from '../src/database';
import { LlmService } from '../src/llm_service';

// A mock database for testing purposes
class MockDatabase implements IDatabase {
  saveRoom(room: any): Promise<void> {
    return Promise.resolve();
  }
  getRoom(roomId: string): Promise<any> {
    return Promise.resolve(null);
  }
}

// A mock LLM service for testing purposes
class MockLlmService extends LlmService {
  override async generate(prompt: string): Promise<string> {
    return Promise.resolve('{}');
  }
}

// A mock JeopardyRoom for testing handlers
export class MockJeopardyRoom extends Room<JeopardyRoomState> {
  broadcastedMessages: { type: string; message: any }[] = [];

  constructor() {
    super();
    this.setState(new JeopardyRoomState());
    this.sessionIdToUidMap = new Map<string, string>();
    this.databaseService = new MockDatabase();
    this.llmService = new MockLlmService();
    this.state.roomSettings = new RoomSettings();
    this.state.playerInfos.push(
      new PlayerInfo({ id: 'player1-id', userName: 'Player 1' })
    );
    this.state.playerInfos.push(
      new PlayerInfo({ id: 'player2-id', userName: 'Player 2' })
    );
    // this.state.playerInfos.push(new PlayerInfo({ id: "host-session-id", userName: "Host" }));
    this.state.playerIds.push('player1-id', 'player2-id');

    this.sessionIdToUidMap.set('player1-id', 'player1-id');
    this.sessionIdToUidMap.set('player2-id', 'player2-id');
    // this.sessionIdToUidMap.set("host-session-id", "host-session-id");
  }

  // Override broadcast to spy on it
  broadcast(type: string, message?: any, options?: any): void {
    this.broadcastedMessages.push({ type, message });
  }

  databaseService: IDatabase;
  llmService: LlmService;

  // Helper to set the game mode for tests
  setGameMode(mode: GameMode) {
    this.state.mode = mode;
    this.state.roomSettings.gameMode = mode;
  }
}

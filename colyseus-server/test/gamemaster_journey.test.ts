import { strict as assert } from 'assert';
import { Client } from 'colyseus';
import { MockJeopardyRoom } from './mock_room';
import {
  GameMode,
  RoundState,
  CategoryState,
  ClueState,
  RoomStateProgress,
  RoundData,
  Category,
  Clue
} from '../src/room_schema';
import { selectClueHandler } from '../src/handlers/select_clue_handler';
import {
  gameMasterActionHandler,
  GameMasterAction
} from '../src/handlers/gamemaster_action_handler';
import { assignPointsHandler } from '../src/handlers/assign_points_handler';
import { endGameHandler } from '../src/handlers/end_game_handler';
import { ServiceRegistry } from '../src/services';
import { FakeDatabase } from '../src/fake_database';
import { FakeAuthService } from './test_helpers';

describe('GameMaster Journey', () => {
  let room: MockJeopardyRoom;
  let hostClient: Client;
  let player1: Client;

  beforeEach(() => {
    room = new MockJeopardyRoom();
    hostClient = { sessionId: 'player2-id' } as Client;
    player1 = { sessionId: 'player1-id' } as Client;

    room.state.host = hostClient.sessionId;
    room.state.roomSettings.gameMode = GameMode.GAMEMASTER;
    room.setGameMode(GameMode.GAMEMASTER);

    const rd = new RoundData();
    const cat = new Category();
    const clue = new Clue();
    clue.value = 200;
    cat.clues.push(clue);
    rd.categories.push(cat);
    room.state.roomData.rounds.push(rd);

    // Mock a fully generated game state
    room.state.gameState.progressType = 'RoomState';
    room.state.gameState.roomState = new RoomStateProgress();
    const roundState = new RoundState();
    const categoryState = new CategoryState();
    categoryState.clueStates.push(new ClueState(), new ClueState());
    roundState.categoryStates.push(categoryState);
    room.state.gameState.roomState.roundStates.push(roundState);
    ServiceRegistry.initialize({
      database: new FakeDatabase(),
      auth: new FakeAuthService()
    });
  });

  afterEach(() => {
    ServiceRegistry.destroy();
  });

  it('should complete a full game flow from start to finish', () => {
    // 1. Host starts the game (already in RoomState for this test)
    assert.equal(room.state.gameState.progressType, 'RoomState');

    // 2. Host selects a clue
    selectClueHandler.call(room, hostClient, {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0
    });
    const activeRound = room.state.gameState.roomState.roundStates[0];
    assert.equal(activeRound.currentCategoryIdx, 0);
    assert.equal(activeRound.currentClueIdx, 0);

    // 3. Host reveals a hint
    gameMasterActionHandler.call(room, hostClient, {
      action: GameMasterAction.TOGGLE_HINT
    });
    const clueState = activeRound.categoryStates[0].clueStates[0];
    assert.equal(clueState.hintOpened, true, 'Hint should be opened');

    // 4. Host assigns points to a player (acting as a team)
    const player1Info = room.state.playerInfos.find(
      p => p.id === player1.sessionId
    );
    player1Info.score = 0; // reset score
    assignPointsHandler.call(room, hostClient, {
      team: 'Player 1'
    });
    assert.equal(player1Info.score, 200, 'Player 1 score should be 200');

    // 5. Host ends the game
    endGameHandler.call(room, hostClient);
    assert.equal(
      room.state.gameState.progressType,
      'RoomSummary',
      'Game should be in summary state'
    );
    assert.ok(
      room.state.gameState.roomSummary,
      'Summary state should be created'
    );
    assert.equal(
      room.state.gameState.roomSummary.originalState.roundStates[0]
        .currentClueIdx,
      0,
      'Final state should be copied'
    );
  });
});

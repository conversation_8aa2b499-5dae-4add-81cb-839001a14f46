import { ColyseusTestServer, boot } from '@colyseus/testing';
import { strict as assert } from 'assert';
import { JeopardyRoom } from '../src/rooms/JeopardyRoom';
import { GameMode, RoomSettings } from '../src/room_schema';
import appConfig from '../src/app.config';
import { LlmService } from '../src/llm_service';
import { MockDatabase } from '../src/mock_database';
import { FakeDatabase } from '../src/fake_database';
import { IAuth } from '../src/auth';
import { DecodedIdToken } from 'firebase-admin/auth';
import { FakeAuthService } from './test_helpers';
import { ServiceRegistry } from '../src/services';

class MockLlmServiceForIntegration extends LlmService {
  private callCount = 0;

  override async generate(prompt: string): Promise<string> {
    this.callCount++;

    console.log(this.callCount, prompt.slice(0, 100));
    // The first call is for initial generation, the second is for regeneration.
    if (this.callCount > 1) {
      const newClue = {
        questionHTML: '<div>This is a regenerated question?</div>',
        answer: 'Regenerated Answer',
        hint: 'Regenerated Hint',
        detailedFactsAboutAnswer: ['Regenerated Fact 1']
      };
      console.log("Regenerating clue...");
      return '```json\n' + JSON.stringify(newClue) + '\n```';
    }
    console.log("First clue...");
    const fakeApiResponse = [
      {
        categoryTitle: 'Test Category',
        questions: [
          {
            answer: 'Answer 2',
            value: 200,
            questionHTML: '<div>Question 2</div>',
            hint: 'Hint 2',
            detailedFactsAboutAnswer: ['Fact 2']
          },
          {
            answer: 'Answer 1',
            value: 100,
            questionHTML: '<div>Question 1</div>',
            hint: 'Hint 1',
            detailedFactsAboutAnswer: ['Fact 1']
          }
        ]
      }
    ];
    return '```json\n' + JSON.stringify(fakeApiResponse) + '\n```';
  }
}

describe('GameMaster Integration Test', () => {
  let colyseus: ColyseusTestServer;
  let db: FakeDatabase;

  beforeEach(async () => {
    db = new FakeDatabase();
    ServiceRegistry.initialize({
      database: db,
      auth: new FakeAuthService(),
      llm: new MockLlmServiceForIntegration()
    });
    colyseus = await boot(appConfig);
  });

  afterEach(async () => {
    console.log('Shutting down Colyseus server in afterEach...');
    if (colyseus) {
      await colyseus.shutdown();
    }
    console.log('Colyseus server shut down.');
    ServiceRegistry.destroy();
  });

  it('should handle the full GameMaster flow', async () => {
    // 1. Create a room and connect host
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.GAMEMASTER
    });
    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });

    // 2. Set categories
    const settings: Partial<RoomSettings> = {
      categoryTitles: ['Test Category'],
      teamNames: ['Team 1', 'Team 2']
    };
    host.send('updateRoomSettings', settings);
    await room.waitForNextPatch();
    assert.deepStrictEqual(Array.from(room.state.categoryTitles), [
      'Test Category'
    ]);

    // 3. Start the game
    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    assert.equal(room.state.gameState.progressType, 'RoomState');
    assert.equal(room.state.roomData.rounds[0].categories[0].clues.length, 2);

    // 4. Select a clue
    host.send('selectClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    assert.equal(
      room.state.gameState.roomState.roundStates[0].currentClueIdx,
      0
    );

    // 5. Assign points
    host.send('assignPoints', { team: 'Team 1', points: 100 });
    await room.waitForNextPatch();
    assert.equal(
      room.state.playerInfos.find(p => p.id === 'team_0')?.score,
      100
    );

    // 6. Regenerate a clue
    const originalQuestion =
      room.state.roomData.rounds[0].categories[0].clues[0].questionHTML;
    host.send('regenerateClue', { roundIdx: 0, categoryIdx: 0, clueIdx: 0 });
    await room.waitForNextPatch();
    const newQuestion =
      room.state.roomData.rounds[0].categories[0].clues[0].questionHTML;
    assert.notStrictEqual(originalQuestion, newQuestion);
    assert.equal(newQuestion, '<div>This is a regenerated question?</div>');

    // 7. End the game
    host.send('endGame');
    await room.waitForNextPatch();
    assert.equal(room.state.gameState.progressType, 'RoomSummary');
  });

  it('should load an existing room from the database', async () => {
    // 1. Create a room and set some state
    const room1 = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.GAMEMASTER
    });
    const host1 = await colyseus.connectTo(room1, { token: 'host-uid-token' });
    host1.send('updateRoomSettings', { categoryTitles: ['Saved Category'] });
    await room1.waitForNextPatch();

    // Manually save the state to the mock database
    await db.saveRoom(room1.state);

    // Disconnect the client to allow the room to dispose
    await host1.leave();

    // 2. Rejoin the room using the getOrCreate endpoint
    const seatReservation = await colyseus.sdk.http.post(
      '/getOrCreate/jeopardy_room',
      {
        body: {
          roomId: room1.roomId,
          token: 'host-uid-token'
        }
      }
    );

    // 3. Assert that the state was loaded correctly
    assert.equal(seatReservation.data.room.roomId, room1.roomId);
  });

  it("should allow host to update a question's content", async () => {
    // 1. Create a room and start the game
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.GAMEMASTER
    });
    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    host.send('updateRoomSettings', { categoryTitles: ['Test Category'] });
    await room.waitForNextPatch();
    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    // 2. Define the updated clue content
    const updatedClue = {
      questionHTML: '<div>This is the new question?</div>',
      answer: 'New Answer',
      hint: 'New Hint'
    };

    // 3. Send the update message
    host.send('updateQuestion', {
      roundIdx: 0,
      categoryIdx: 0,
      clueIdx: 0,
      updatedClue
    });
    await room.waitForNextPatch();

    // 4. Assert that the clue was updated in the room state
    const clueInState = room.state.roomData.rounds[0].categories[0].clues[0];
    assert.equal(clueInState.questionHTML, updatedClue.questionHTML);
    assert.equal(clueInState.answer, updatedClue.answer);
    assert.equal(clueInState.hint, updatedClue.hint);
    assert.equal(clueInState.value, 100, 'Point value should not change');
  });

  it('should allow host to reorder questions', async () => {
    // 1. Create a room and start the game
    const room = await colyseus.createRoom('jeopardy_room', {
      mode: GameMode.GAMEMASTER
    });
    const host = await colyseus.connectTo(room, { token: 'host-uid-token' });
    host.send('updateRoomSettings', {
      categoryTitles: ['Reorder Category']
    });
    await room.waitForNextPatch();
    host.send('startRoom');
    await room.waitForNextPatch();
    await room.waitForNextPatch();

    // 2. Define the reordered clues
    const originalClues = room.state.roomData.rounds[0].categories[0].clues.toJSON();
    const reorderedClues = [originalClues[1], originalClues[0]]; // Swap them

    // 3. Send the reorder message
    host.send('reorderQuestionsWithAnswerStates', {
      roundIdx: 0,
      reorderedCategories: [
        {
          categoryIdx: 0,
          clues: reorderedClues
        }
      ]
    });
    await room.waitForNextPatch();

    // 4. Assert that the clues were reordered but values are preserved
    const cluesInState = room.state.roomData.rounds[0].categories[0].clues;
    assert.equal(cluesInState[0].answer, 'Answer 2');
    assert.equal(cluesInState[0].value, 200); // Original value
    assert.equal(cluesInState[1].answer, 'Answer 1');
    assert.equal(cluesInState[1].value, 100); // Original value
  });
});

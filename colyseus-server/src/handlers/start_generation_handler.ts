import { <PERSON>opardyRoom } from '../rooms/JeopardyRoom';
import { generateRoundSimple } from '../round_generator';
import {
  RoomStateProgress,
  RoundData,
  RoundState,
  CategoryState,
  ClueState,
  ArraySchema,
  GeneratingQuestions
} from '../room_schema';
import { StructuredClueLog } from '../structured_log';
import { ServiceRegistry } from '../services';

export async function startGenerationHandler(
  this: JeopardyRoom,
  client: { sessionId: string }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  console.log(`Start generation request: Received from client ${uid}.`);

  // 1. Verify the client is the host and the game is in the WaitingRoom state.
  if (uid !== this.state.host) {
    console.warn(
      `Start generation failed: Client ${uid} attempted to start the game but is not the host.`
    );
    return;
  }

  if (this.state.gameState.progressType !== 'WaitingRoom') {
    console.warn(
      `Start generation failed: Host ${uid} attempted to start the game, but it has already started.`
    );
    return;
  }

  console.log(
    `Host ${uid} is starting the game. Transitioning to question generation.`
  );

  try {
    // 2. Transition the game state to "GeneratingQuestions".
    this.state.gameState.progressType = 'GeneratingQuestions';
    this.broadcastPatch(); // Immediately notify clients of the state change
    console.log(
      "Game state transitioned to 'GeneratingQuestions'. Notifying clients."
    );

    // 3. Call the RoundGenerator service.
    console.log(`Generating round with the following settings:
        Categories: [${this.state.categoryTitles.join(', ')}]
        Number of Questions per Category: ${
          this.state.roomSettings.numQuestions
        }
        LLM Agent: ${this.state.roomSettings.llmAgent.provider} - ${
      this.state.roomSettings.llmAgent.modelName
    }`);
    this.state.gameState.generatingQuestions = new GeneratingQuestions();
    // this.state.gameState.generatingQuestions.streamedResponse = '';
    const rounds = await generateRoundSimple(
      Array.from(this.state.categoryTitles),
      this.state.roomSettings.numQuestions,
      this.state.categoryTitles.length,
      this.state.roomSettings.llmAgent,
      this.state.roomSettings.generationStrategy,
      (text, questionsGenerated) => {
        this.state.gameState.generatingQuestions.questionsGenerated.clear();
        this.state.gameState.generatingQuestions.questionsGenerated.push(
          ...questionsGenerated
        );
        // this.state.gameState.generatingQuestions.streamedResponse += text;
        this.broadcast('generationChunk', text);
      }
    );
    console.log('Round data received from generation service.');

    // 4. On success, populate roomData and transition to RoomState.
    const roundData = new RoundData();
    roundData.categories.push(...rounds[0].categories); // Assuming generateRoundSimple returns one round
    this.state.roomData.rounds.push(roundData);

    this.state.gameState.progressType = 'RoomState';
    this.state.gameState.roomState = new RoomStateProgress();
    console.log(
      "Game state transitioned to 'RoomState'. Initializing game board."
    );

    // Initialize roundStates, categoryStates, and clueStates
    const roundState = new RoundState();
    for (const category of rounds[0].categories) {
      const categoryState = new CategoryState();
      for (const clue of category.clues) {
        const clueState = new ClueState();
        clueState.structuredClueLog = new StructuredClueLog();
        // clueState.clueLog = new ClueLog();
        categoryState.clueStates.push(clueState);
      }
      roundState.categoryStates.push(categoryState);
    }
    this.state.gameState.roomState.roundStates.push(roundState);
    console.log('Game board state initialized successfully.');

    // 5. Persist the room state.
    await ServiceRegistry.getInstance().database.saveRoom(this.state);
    console.log('Initial game state saved to database.');
  } catch (error) {
    console.error('Error generating questions:', error);
    // 6. On failure, revert the game state to WaitingRoom.
    this.state.gameState.progressType = 'WaitingRoom';
    console.log(
      "Question generation failed. Reverting game state to 'WaitingRoom'."
    );
    // Optionally, send an error message to the clients.
    this.broadcast('error', 'Failed to generate questions. Please try again.');
  }
}

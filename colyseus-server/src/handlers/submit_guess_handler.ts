import { Client } from 'colyseus';
import { JeopardyRoom } from '../rooms/JeopardyRoom';
import {
  ClueState,
  GameMode,
  AnswerExplanation,
  LlmAgent
} from '../room_schema';
import levenshtein from 'js-levenshtein';
import {
  advanceTurn,
  clearAnswerTimer,
  deductPoints,
  endClue
} from './clue_lifecycle_helpers';
import { addStructuredLogEntry } from './log_utils';
import {
  AnswerCorrectLog,
  AnswerIncorrectLog,
  LogType,
  PassLog
} from '../structured_log';
import { ServiceRegistry } from '../services';
import { extractLastJsonObject } from '../round_generator';

const MAX_LEVENSHTEIN_DISTANCE = 2;

export interface SubmitGuessPayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  guess: string;
}

async function checkAnswer(
  room: JeopardyRoom,
  payload: SubmitGuessPayload
): Promise<AnswerExplanation> {
  const { roundIdx, categoryIdx, clueIdx, guess } = payload;
  const clueData =
    room.state.roomData.rounds[roundIdx].categories[categoryIdx].clues[clueIdx];
  const categoryTitle =
    room.state.roomData.rounds[roundIdx].categories[categoryIdx].categoryTitle;

  const dist = levenshtein(clueData.answer.toLowerCase(), guess.toLowerCase());
  console.log(
    `Levenshtein distance between answers ${clueData.answer} and ${guess}: ${dist}`
  );
  if (dist == 0) {
    return new AnswerExplanation({
      isCorrect: true,
      explanation: 'Exact match!!'
    });
  }

  // 1. Naive check for simple typos
  if (dist <= MAX_LEVENSHTEIN_DISTANCE) {
    return new AnswerExplanation({
      isCorrect: true,
      explanation: 'Your answer was close enough!'
    });
  }

  // 2. LLM check for more complex variations
  try {
    const prompt = `
      In a trivia game, under the category "${categoryTitle}", a player is asked: "${clueData.questionHTML.replace(
      /<[^>]*>/g,
      ''
    )}"
      The player responds: "${guess}"
      The correct answer is: "${clueData.answer}"
      Is the player's answer acceptable as correct? Consider synonyms, alternate phrasings, and common knowledge. Explain your reasoning.
      Answer as a JSON object: { "isCorrect": boolean, "explanation": "short_explanation" }`;

    console.log('Asking LLM to check answer: ${prompt}');
    let agent = new LlmAgent();
    agent.provider = 'Gemini';
    agent.modelName = 'gemini-2.5-flash-lite-preview-06-17';

    const llmResponse = await ServiceRegistry.getInstance().llm.generate(
      prompt,
      agent
    );
    const parsed = JSON.parse(extractLastJsonObject(llmResponse)!) as {
      isCorrect: boolean;
      explanation: string;
    };
    return new AnswerExplanation(parsed);
  } catch (error) {
    console.error('Error checking answer with LLM:', error);
    // Fallback to naive check on error
    return new AnswerExplanation({
      isCorrect: false,
      explanation: 'There was an error validating the answer.'
    });
  }
}

export async function submitGuessHandler(
  this: JeopardyRoom,
  client: Client,
  payload: SubmitGuessPayload
) {
  const { roundIdx, categoryIdx, clueIdx, guess } = payload;
  const uid = this.sessionIdToUidMap.get(client.sessionId);

  console.log(
    `Submit guess request: Received from client ${uid} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}]. Guess: "${guess}"`
  );

  if (!uid) {
    console.warn('Submit guess failed: Guess submitted by unknown client.');
    return;
  }

  const roomState = this.state.gameState.roomState;
  if (!roomState) {
    console.warn(
      `Submit guess failed: Guess submitted for inactive clue by player ${uid}.`
    );
    return;
  }

  const clueState: ClueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];

  // Validate that it's the player's turn
  const expectedPlayer =
    clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];
  if (uid !== expectedPlayer) {
    console.warn(
      `Submit guess failed: Guess submitted by player ${uid} out of turn. Expected player: ${expectedPlayer}.`
    );
    return;
  }

  clearAnswerTimer(this, roundIdx, categoryIdx, clueIdx);
  const player = this.state.playerInfos.find(p => p.id === uid);

  if (guess === '<PASSING>') {
    console.log(`Player ${uid} passed their turn.`);
    const pointsToDeduct = deductPoints(
      this,
      roundIdx,
      categoryIdx,
      clueIdx,
      uid,
      false
    );
    const logEntry = new PassLog();
    logEntry.assign({
      type: LogType.PASS,
      playerId: uid,
      playerName: player.userName,
      pointsDeducted: pointsToDeduct
    });
    addStructuredLogEntry(this, clueState, logEntry);
    advanceTurn(this, roundIdx, categoryIdx, clueIdx, false);
    return;
  }

  const answerExplanation = await checkAnswer(this, payload);
  clueState.answerExplanations.push(answerExplanation);
  console.log(
    `Guess evaluated as ${
      answerExplanation.isCorrect ? 'correct' : 'incorrect'
    }. Explanation: ${answerExplanation.explanation}`
  );

  if (answerExplanation.isCorrect) {
    // Award points and mark clue as complete
    if (player) {
      let points = this.state.roomData.rounds[roundIdx].categories[categoryIdx]
        .clues[clueIdx].value;
      if (clueState.hintOpened) {
        points /= 2;
        console.log(`Hint was opened, so points are halved to ${points}.`);
      }
      player.score += points;
      const logEntry = new AnswerCorrectLog();
      logEntry.assign({
        type: LogType.ANSWER_CORRECT,
        playerId: uid,
        playerName: player.userName,
        guess,
        pointsAwarded: points
      });
      addStructuredLogEntry(this, clueState, logEntry);
      console.log(
        `Awarded ${points} points to player ${uid}. New score: ${player.score}.`
      );
    }
    clueState.answeredByPlayerId = uid;
    if (this.state.mode === GameMode.BUZZER) {
      const playerIndex = this.state.playerIds.findIndex(id => id === uid);
      if (playerIndex !== -1) {
        roomState.currentPlayerIdx = playerIndex;
        console.log(
          `Buzzer mode: Player ${uid} answered correctly. Setting them as the current player (index: ${playerIndex}).`
        );
      }
    }
    endClue(this, roundIdx, categoryIdx, clueIdx);
  } else {
    // Incorrect answer, advance to the next player or phase
    const pointsToDeduct = deductPoints(
      this,
      roundIdx,
      categoryIdx,
      clueIdx,
      uid,
      false
    );
    const logEntry = new AnswerIncorrectLog();
    logEntry.assign({
      type: LogType.ANSWER_INCORRECT,
      playerId: uid,
      playerName: player.userName,
      guess,
      pointsDeducted: pointsToDeduct
    });
    addStructuredLogEntry(this, clueState, logEntry);
    advanceTurn(this, roundIdx, categoryIdx, clueIdx, false); // isTimeout = false
  }
}

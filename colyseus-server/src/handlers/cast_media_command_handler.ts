import { <PERSON>opardyRoom } from '../rooms/JeopardyRoom';

export interface CastMediaCommand {
  type: 'play' | 'pause' | 'seek' | 'volume' | 'pauseAll' | 'mute' | 'unmute';
  questionId?: string;
  value?: number;
  timestamp: number;
}

export function castMediaCommandHandler(
  this: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  client: { sessionId: string },
  message: { command: CastMediaCommand }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  console.log(
    `Received castMediaCommand from ${uid}:`,
    JSON.stringify(message.command, null, 2)
  );

  // Only the host can send media commands.
  if (uid !== this.state.host) {
    console.warn(
      `Cast media command failed: Client ${uid} attempted to send a command but is not the host.`
    );
    return;
  }

  // Broadcast the command to all clients. The cast client will pick this up.
  console.log('Broadcasting castMediaCommand to all clients.');
  this.broadcast('castMediaCommand', message.command);
}

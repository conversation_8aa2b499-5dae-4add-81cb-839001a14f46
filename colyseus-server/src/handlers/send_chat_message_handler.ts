import { Client } from 'colyseus';
import { <PERSON><PERSON>ardyRoom } from '../rooms/JeopardyRoom';
import { ChatMessage } from '../room_schema';

export function sendChatMessage(
  this: <PERSON><PERSON><PERSON>y<PERSON><PERSON>,
  client: Client,
  message: { content: string }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  if (!uid) {
    console.error('Chat message failed: Could not find UID for client.');
    return;
  }

  const playerInfo = this.state.playerInfos.find(p => p.id === uid);
  if (!playerInfo) {
    console.error(
      `Chat message failed: Could not find player info for UID ${uid}.`
    );
    return;
  }

  if (!message.content || message.content.trim() === '') {
    console.error('Chat message failed: Message content is empty.');
    return;
  }

  const chatMessage = new ChatMessage({
    senderId: uid,
    senderName: playerInfo.userName,
    content: message.content,
    timestamp: Date.now()
  });

  this.broadcast('chat_message', chatMessage);
}

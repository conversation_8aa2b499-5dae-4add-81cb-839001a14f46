import { <PERSON>opardyRoom } from '../rooms/JeopardyRoom';
import { GameMode, Clue } from '../room_schema';
import { ServiceRegistry } from '../services';

export interface ReorderCategory {
  categoryIdx: number;
  clues: Partial<Clue>[];
}

export interface ReorderQuestionsPayload {
  roundIdx: number;
  reorderedCategories: ReorderCategory[];
}

export function reorderQuestionsHandler(
  this: JeopardyRoom,
  client: { sessionId: string },
  payload: ReorderQuestionsPayload
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { roundIdx, reorderedCategories } = payload;

  // 1. Validate user is host and in Gamemaster mode
  if (uid !== this.state.host || this.state.mode !== GameMode.GAMEMASTER) {
    console.warn('Reorder questions failed: Unauthorized attempt.');
    return;
  }

  const roundData = this.state.roomData.rounds?.[roundIdx];
  if (!roundData) {
    console.error('Reorder questions failed: Round not found.');
    return;
  }

  // 2. Update the categories with the new clue order and values
  reorderedCategories.forEach(cat => {
    const categoryToUpdate = roundData.categories[cat.categoryIdx];
    if (categoryToUpdate) {
      // Clear existing clues and push the reordered ones
      categoryToUpdate.clues.clear();
      cat.clues.forEach(clueData => {
        const newClue = new Clue(clueData);
        categoryToUpdate.clues.push(newClue);
      });
    }
  });

  console.log(`Questions for round ${roundIdx} reordered successfully.`);

  // 3. Persist the state
  ServiceRegistry.getInstance().database.saveRoom(this.state);
}

import { Je<PERSON>ardyRoom } from '../rooms/JeopardyRoom';
import { GameMode } from '../room_schema';
import { addStructuredLogEntry } from './log_utils';
import { LogType, PointsAssignedLog } from '../structured_log';
import { ServiceRegistry } from '../services';
import { endClue } from './clue_lifecycle_helpers';

export function assignPointsHandler(
  this: JeopardyRoom,
  client: { sessionId: string },
  message: { team: string }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { team } = message;

  // 1. Verify the client is the host and in Gamemaster mode
  if (uid !== this.state.host) {
    console.warn(
      `Point assignment failed: Client ${uid} attempted to assign points but is not the host.`
    );
    return;
  }
  if (this.state.mode !== GameMode.GAMEMASTER) {
    console.warn(
      `Point assignment failed: Host ${uid} attempted to assign points, but the mode is not GAMEMASTER.`
    );
    return;
  }

  // 2. Get the current clue to determine the points
  const roomState = this.state.gameState.roomState;
  if (
    !roomState ||
    roomState.roundStates[roomState.roundIdx].currentClueIdx === -1
  ) {
    console.warn('Point assignment failed: No clue is active.');
    return;
  }
  const { roundIdx } = roomState;
  const { currentCategoryIdx, currentClueIdx } = roomState.roundStates[
    roundIdx
  ];
  const clue = this.state.roomData.rounds[roundIdx].categories[
    currentCategoryIdx
  ].clues[currentClueIdx];
  const points = clue.value;

  console.log(
    `Assigning points: Host (${uid}) requested to give ${points} points to team ${team}.`
  );

  // 3. Find the team and update their score
  const player = this.state.playerInfos.find(p => p.userName === team);
  if (player) {
    player.score += points;
    console.log(
      `Points assigned successfully. Team ${team}'s new score is ${player.score}.`
    );

    // 4. Mark the current clue as complete and log it
    const clueState = roomState.roundStates[roundIdx]?.categoryStates[
      currentCategoryIdx
    ]?.clueStates[currentClueIdx]!;
    const logEntry = new PointsAssignedLog();
    logEntry.assign({
      type: LogType.POINTS_ASSIGNED,
      playerId: player.id,
      playerName: player.userName,
      pointsAwarded: points
    });
    if (player) {
      clueState.answeredByPlayerId = team;
    }
    addStructuredLogEntry(this, clueState, logEntry);
  }
  endClue(this, roundIdx, currentCategoryIdx, currentClueIdx);
  console.log(
    `Clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}] is being ended after points assignment.`
  );
}

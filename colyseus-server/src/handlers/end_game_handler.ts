
import { Client } from 'colyseus';
import { <PERSON><PERSON><PERSON>yRoom } from '../rooms/JeopardyRoom';
import { RoomSummary, RoomState, MemorableQuestion } from '../room_schema';

export function endGameHandler(this: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, client: Client) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  console.log(`End game request: Received from client ${uid}.`);

  // Only the host can end the game
  if (uid !== this.state.host) {
    console.error(
      `End game failed: Client ${uid} is not the host, cannot end the game.`
    );
    return;
  }

  // Game must be in progress to be ended
  if (this.state.gameState.progressType !== 'RoomState') {
    console.error(
      `End game failed: Game is not in progress (current state: ${this.state.gameState.progressType}), cannot end the game.`
    );
    return;
  }

  console.log(`Host ${uid} is ending the game. Transitioning to summary state.`);

  // Create a new RoomState object from the current RoomStateProgress
  const finalState = new RoomState();
  const currentProgress = this.state.gameState.roomState;
  finalState.roundIdx = currentProgress.roundIdx;
  finalState.currentPlayerIdx = currentProgress.currentPlayerIdx;
  finalState.roundStates = currentProgress.roundStates.clone();
  console.log('Final game state captured for summary.');

  // --- Generate Memorable Questions ---
  const memorableQuestions: MemorableQuestion[] = [];
  try {
    const roomData = this.state.roomData;
    const roomState = this.state.gameState.roomState;

    for (
      let rIdx = 0;
      rIdx < roomState.roundStates.length && memorableQuestions.length < 3;
      rIdx++
    ) {
      const roundState = roomState.roundStates[rIdx];
      const roundData = roomData.rounds[rIdx];
      for (
        let cIdx = 0;
        cIdx < roundState.categoryStates.length &&
        memorableQuestions.length < 3;
        cIdx++
      ) {
        const categoryState = roundState.categoryStates[cIdx];
        const categoryData = roundData.categories[cIdx];
        for (
          let qIdx = 0;
          qIdx < categoryState.clueStates.length &&
          memorableQuestions.length < 3;
          qIdx++
        ) {
          const clueState = categoryState.clueStates[qIdx];
          if (clueState.clueComplete) {
            const clueData = categoryData.clues[qIdx];
            memorableQuestions.push(
              new MemorableQuestion({
                category: categoryData.categoryTitle,
                question: clueData.questionHTML,
                answer: clueData.answer,
                value: clueData.value,
                reason: 'This was one of the memorable questions from the game.'
              })
            );
          }
        }
      }
    }
    console.log(
      `Successfully generated ${memorableQuestions.length} memorable questions.`
    );
  } catch (error) {
    console.error('Error creating memorable questions:', error);
  }
  // --- End Generation ---

  // Transition to the summary state
  this.state.gameState.progressType = 'RoomSummary';
  const summary = new RoomSummary();
  summary.originalState = finalState;
  summary.memorableQuestions.push(...memorableQuestions); // Add the generated questions
  this.state.gameState.roomSummary = summary;
  console.log('Successfully transitioned to RoomSummary state.');

  // Clean up the old state properties
  this.state.gameState.roomState = undefined;
  this.state.gameState.waitingRoom = undefined;
  this.state.gameState.generatingQuestions = undefined;
  console.log('Cleaned up previous game state properties.');
}


import { Client } from 'colyseus';
import { <PERSON><PERSON><PERSON>yRoom } from '../rooms/JeopardyRoom';
import { ClueState } from '../room_schema';
import { startAnswerTimer } from './clue_lifecycle_helpers';
import { addStructuredLogEntry } from './log_utils';
import { BuzzInLog, LogType } from '../structured_log';

export interface BuzzInPayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export function buzzInHandler(
  this: JeopardyRoom,
  client: Client,
  payload: BuzzInPayload
) {
  const { roundIdx, categoryIdx, clueIdx } = payload;
  const uid = this.sessionIdToUidMap.get(client.sessionId);

  console.log(
    `Buzz-in: Player ${uid} is attempting to buzz in for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  if (!uid) {
    console.warn('Buzz-in failed: Buzz-in from unknown client.');
    return;
  }

  const roomState = this.state.gameState.roomState;
  if (
    !roomState ||
    roomState.roundStates[roundIdx]?.currentClueIdx !== clueIdx
  ) {
    console.warn(
      `Buzz-in failed: Player ${uid} buzzed in for an inactive clue.`
    );
    return;
  }

  const clueState: ClueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];

  // Validations
  if (clueState.clueComplete) {
    console.warn(
      `Buzz-in failed: Player ${uid} attempted to buzz in on a completed clue.`
    );
    return;
  }
  if (clueState.passedPlayers.includes(uid)) {
    console.warn(
      `Buzz-in failed: Player ${uid} has already answered incorrectly in this phase.`
    );
    return;
  }
  if (clueState.buzzedInPlayerQueue.includes(uid)) {
    console.warn(
      `Buzz-in failed: Player ${uid} is already in the buzz-in queue.`
    );
    return;
  }
  if (clueState.passedPlayers.includes(uid)) {
    console.warn(
      `Buzz-in failed: Player ${uid} has already passed. Can't buzz in.`
    );
    return;
  }
  const buzzInDuration =
    this.clock.currentTime - clueState.buzzInTimerStartTime;

  console.log(`Player ${uid} buzz-in time: ${buzzInDuration}ms.`);
  if (buzzInDuration > this.state.roomSettings.buzzInTimerDurationMillis) {
    console.warn(`Buzz-in failed: Player ${uid}'s buzz-in timer expired.`);
    return;
  }

  // Add player to the queue
  clueState.buzzedInPlayerQueue.push(uid);
  console.log(
    `Player ${uid} successfully added to the buzz-in queue. Queue is now: [${clueState.buzzedInPlayerQueue.join(
      ', '
    )}].`
  );

  // Add structured log entry
  const player = this.state.playerInfos.find(p => p.id === uid);
  const logEntry = new BuzzInLog();
  logEntry.assign({
    type: LogType.BUZZ_IN,
    playerId: uid,
    playerName: player.userName,
    timeTaken: buzzInDuration / 1000
  });
  addStructuredLogEntry(this, clueState, logEntry);

  // If this is the first player, start their answer timer
  if (clueState.buzzedInPlayerQueue.length === 1) {
    clueState.queueAnswerTurnIdx = 0;
    startAnswerTimer(this, roundIdx, categoryIdx, clueIdx);
    console.log(
      `Player ${uid} is the first to buzz in. Starting their answer timer.`
    );
  }
}

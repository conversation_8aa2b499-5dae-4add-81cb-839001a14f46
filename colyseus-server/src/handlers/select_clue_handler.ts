import { Je<PERSON><PERSON>yRoom } from '../rooms/JeopardyRoom';
import { GameMode } from '../room_schema';
import {
  checkAndAdvancePhase,
  startAnswerTimer
} from './clue_lifecycle_helpers';
import { addStructuredLogEntry } from './log_utils';
import { ClueSelectedLog, LogType } from '../structured_log';

export function selectClueHandler(
  this: JeopardyRoom,
  client: { sessionId: string },
  message: { roundIdx: number; categoryIdx: number; clueIdx: number }
) {
  const { roundIdx, categoryIdx, clueIdx } = message;
  const uid = this.sessionIdToUidMap.get(client.sessionId);

  console.log(
    `Select clue request: Received from client ${uid} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  if (!uid) {
    console.warn(
      `Select clue failed: Unknown client ${client.sessionId} tried to select a clue.`
    );
    return;
  }

  // 1. Basic validation
  let player;
  if (this.state.mode === GameMode.GAMEMASTER) {
    if (this.state.host !== uid) {
      console.warn(
        `Select clue failed: Player ${uid} tried to select a clue in Gamemaster mode, but is not the host.`
      );
      return;
    }
    // In Gamemaster mode, the "player" is the host.
    player = this.state.playerInfos.find(p => p.id === this.state.host);
  } else {
    player = this.state.playerInfos.find(p => p.id === uid);
    if (!player) {
      console.warn(
        `Select clue failed: Unknown player ${uid} tried to select a clue.`
      );
      return;
    }
  }

  if (this.state.gameState.progressType !== 'RoomState') {
    console.warn(
      `Select clue failed: Player ${uid} tried to select a clue, but the game is not in progress.`
    );
    return;
  }

  const roomState = this.state.gameState.roomState;
  if (roomState.roundStates[roundIdx]?.currentClueIdx !== -1) {
    console.warn(
      `Select clue failed: Player ${uid} tried to select a clue, but another clue is already active.`
    );
    return;
  }

  // 2. Turn-based mode validation
  if (
    this.state.mode === GameMode.TURN_BASED &&
    this.state.playerIds[roomState.currentPlayerIdx] !== uid
  ) {
    console.warn(
      `Select clue failed: Player ${uid} tried to select a clue, but it's not their turn.`
    );
    return;
  }

  const clueState =
    roomState.roundStates[roundIdx]?.categoryStates[categoryIdx]?.clueStates[
      clueIdx
    ];
  if (!clueState || clueState.clueComplete) {
    console.warn(
      `Select clue failed: Player ${uid} selected a clue that is invalid or already completed.`
    );
    return;
  }

  // 4. Update game state
  roomState.roundStates[roundIdx].currentCategoryIdx = categoryIdx;
  roomState.roundStates[roundIdx].currentClueIdx = clueIdx;
  clueState.buzzInTimerStartTime = this.clock.currentTime;
  console.log(
    `Game state updated. Active clue is now [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  // 5. Add structured log entry
  const categs = this.state.roomData?.rounds?.[roundIdx]?.categories?.[
    categoryIdx
  ];
  const logEntry = new ClueSelectedLog();
  logEntry.assign({
    type: LogType.CLUE_SELECTED,
    playerId: uid,
    playerName: player?.userName ? player.userName : 'Gamemaster',
    categoryTitle: categs?.categoryTitle || 'Unknown Category',
    clueValue: categs?.clues?.[clueIdx].value || -1
  });
  addStructuredLogEntry(this, clueState, logEntry);

  // 6. Populate answer queue for turn-based mode
  if (this.state.roomSettings.gameMode === GameMode.TURN_BASED) {
    for (let i = 0; i < this.state.playerIds.length; i++) {
      const playerIndex =
        (roomState.currentPlayerIdx + i) % this.state.playerIds.length;
      clueState.buzzedInPlayerQueue.push(this.state.playerIds[playerIndex]);
    }
    clueState.queueAnswerTurnIdx = 0; // It's the first player's turn to answer
    startAnswerTimer(this, roundIdx, categoryIdx, clueIdx);
    console.log(
      `Turn-based mode: Answer queue populated. Order: [${clueState.buzzedInPlayerQueue.join(
        ', '
      )}].`
    );
  } else if (this.state.roomSettings.gameMode === GameMode.BUZZER) {
    // Set a timeout for the FIRST buzz-in phase
    console.log(
      `Buzzer mode (Phase 1): Starting buzz-in timer for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] for ${this.state.roomSettings.buzzInTimerDurationMillis}ms.`
    );
    this.clock.setTimeout(() => {
      console.log(
        `Buzz-in timer (Phase 1) expired for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
      );
      checkAndAdvancePhase(this, roundIdx, categoryIdx, clueIdx);
    }, this.state.roomSettings.buzzInTimerDurationMillis);
  }

  console.log(
    `Clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] selected successfully by ${uid}.`
  );
  this.broadcast('clue_selected', { roundIdx, categoryIdx, clueIdx });
}

import { Jeop<PERSON>yRoom } from '../rooms/JeopardyRoom';
import { completeClue, endClue } from './clue_lifecycle_helpers';

export function skipShowAnswerHandler(
  this: <PERSON>opardyRoom,
  client: { sessionId: string },
  message: { roundIdx: number; categoryIdx: number; clueIdx: number }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { roundIdx, categoryIdx, clueIdx } = message;

  console.log(
    `Skip show answer request: Received from client ${uid} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  const clueState = this.state.gameState.roomState.roundStates[roundIdx]
    .categoryStates[categoryIdx].clueStates[clueIdx];

  if (!clueState.showAnswer) {
    console.warn(
      `Skip show answer failed: The answer for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] is not being shown.`
    );
    return;
  }

  if (!clueState.skipShowAnswer.includes(uid)) {
    clueState.skipShowAnswer.push(uid);
  }

  if (clueState.skipShowAnswer.length >= this.state.playerIds.length) {
    completeClue(this, roundIdx, categoryIdx, clueIdx);
  }
}

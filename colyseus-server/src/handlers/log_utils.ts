import { ArraySchema, ClueState } from '../room_schema';
import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { BaseLogEntry } from '../structured_log';
import { generateSnarkyComment } from './snark_commentary_generator';

/**
 * Adds a structured log entry to the clueState.
 *
 * @param clueState The clue state to update
 * @param logEntry The structured log entry to add
 * @return ClueState The updated clue state
 */
export function addStructuredLogEntry(
  room: JeopardyRoom,
  clueState: ClueState,
  logEntry: BaseLogEntry
): ClueState {
  // Initialize structuredClueLog if it doesn't exist
  if (!clueState.structuredClueLog) {
    clueState.structuredClueLog.entries = new ArraySchema<BaseLogEntry>();
  }
  logEntry.timestamp = room.clock.currentTime;

  // Generate a snarky comment for the log entry
  logEntry.snarkyComment = generateSnarkyComment(logEntry);

  // Add the structured log entry
  clueState.structuredClueLog.entries.push(logEntry);

  return clueState;
}

import { <PERSON><PERSON><PERSON>yRoom } from '../rooms/JeopardyRoom';
import { GameM<PERSON>, Clue } from '../room_schema';
import { ServiceRegistry } from '../services';

export interface UpdateQuestionPayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  updatedClue: Partial<Clue>;
}

export function updateQuestionHandler(
  this: <PERSON><PERSON><PERSON>y<PERSON><PERSON>,
  client: { sessionId: string },
  payload: UpdateQuestionPayload
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { roundIdx, categoryIdx, clueIdx, updatedClue } = payload;

  // 1. Validate user is host and in Gamemaster mode
  if (uid !== this.state.host || this.state.mode !== GameMode.GAMEMASTER) {
    console.warn('Update question failed: Unauthorized attempt.');
    return;
  }

  console.log(
    `Update question request: Received from client ${uid} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  // 2. Validate indices
  const clueToUpdate = this.state.roomData.rounds?.[roundIdx]?.categories?.[
    categoryIdx
  ]?.clues?.[clueIdx];
  if (!clueToUpdate) {
    console.error(
      'Update question failed: Clue not found at specified indices.'
    );
    return;
  }

  // 3. Apply the updates
  if (updatedClue.questionHTML !== undefined) {
    clueToUpdate.questionHTML = updatedClue.questionHTML;
  }
  if (updatedClue.answer !== undefined) {
    clueToUpdate.answer = updatedClue.answer;
  }
  if (updatedClue.hint !== undefined) {
    clueToUpdate.hint = updatedClue.hint;
  }
  if (updatedClue.hintBlanks !== undefined) {
    clueToUpdate.hintBlanks = updatedClue.hintBlanks;
  }
  if (updatedClue.value !== undefined) {
    clueToUpdate.value = updatedClue.value;
  }
  if (updatedClue.detailedFactsAboutAnswer !== undefined) {
    clueToUpdate.detailedFactsAboutAnswer.clear();
    updatedClue.detailedFactsAboutAnswer.forEach(fact =>
      clueToUpdate.detailedFactsAboutAnswer.push(fact)
    );
  }

  console.log(
    `Question [${roundIdx}, ${categoryIdx}, ${clueIdx}] updated successfully.`
  );

  // 4. Persist the state
  ServiceRegistry.getInstance().database.saveRoom(this.state);
}

import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { GameMode } from '../room_schema';
import { addStructuredLogEntry } from './log_utils';
import { HintOpenedLog, LogType, ShowAnswerLog } from '../structured_log';

export enum GameMasterAction {
  TOGGLE_HINT = 'TOGGLE_HINT',
  TOGGLE_ANSWER = 'TOGGLE_ANSWER',
  START_COUNTDOWN = 'START_COUNTDOWN',
  PAUSE_COUNTDOWN = 'PAUSE_COUNTDOWN',
  RESET_COUNTDOWN = 'RESET_COUNTDOWN'
}

export function gameMasterActionHandler(
  this: Jeop<PERSON>yRoom,
  client: { sessionId: string },
  message: { action: GameMasterAction | string; countdownDurationSeconds?: number }
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { action, countdownDurationSeconds } = message;
  console.log(
    `Gamemaster action: Received action '${action}' from client ${uid}.`
  );

  // 1. Verify the client is the host and in Gamemaster mode
  if (uid !== this.state.host) {
    console.warn(
      `Gamemaster action failed: Client ${uid} attempted a Gamemaster action but is not the host.`
    );
    return;
  }
  if (this.state.mode !== GameMode.GAMEMASTER) {
    console.warn(
      `Gamemaster action failed: Host ${uid} attempted a Gamemaster action, but the mode is not GAMEMASTER.`
    );
    return;
  }

  // 2. Get the active clue state
  const roomState = this.state.gameState.roomState;
  if (
    !roomState ||
    !roomState.roundStates[roomState.roundIdx] ||
    roomState.roundStates[roomState.roundIdx].currentClueIdx === -1
  ) {
    console.warn('Gamemaster action failed: No clue is active.');
    return;
  }

  const { roundIdx } = roomState;
  const roundState = roomState.roundStates[roundIdx];
  const { currentCategoryIdx, currentClueIdx } = roundState;
  const clueState =
    roundState.categoryStates[currentCategoryIdx]?.clueStates[currentClueIdx];

  if (!clueState) {
    console.error(
      'Gamemaster action failed: Could not find active clue state.'
    );
    return;
  }

  // 3. Perform the action
  switch (action) {
    case 'toggleHint':
    case GameMasterAction.TOGGLE_HINT:
      clueState.hintOpened = !clueState.hintOpened;
      if (clueState.hintOpened) {
        const logEntry = new HintOpenedLog();
        logEntry.assign({
          type: LogType.HINT_OPENED
        });
        addStructuredLogEntry(this, clueState, logEntry);
      }
      console.log(
        `Gamemaster action successful: Host toggled hint for clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}]. New hintOpened state: ${clueState.hintOpened}`
      );
      break;
    case 'toggleAnswer':
    case GameMasterAction.TOGGLE_ANSWER:
      clueState.showAnswer = !clueState.showAnswer;
      if (clueState.showAnswer) {
        const clue = this.state.roomData?.rounds?.[roundIdx]?.categories[
          currentCategoryIdx
        ]?.clues?.[currentClueIdx];
        const logEntry = new ShowAnswerLog();

        logEntry.assign({
          type: LogType.SHOW_ANSWER,
          answer: clue?.answer
        });
        addStructuredLogEntry(this, clueState, logEntry);
        clueState.clueComplete = true;
        console.log(
          `Clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}] marked as complete because the answer was shown.`
        );
      }
      console.log(
        `Gamemaster action successful: Host toggled answer for clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}]. New showAnswer state: ${clueState.showAnswer}`
      );
      break;
    case 'startCountdown':
    case GameMasterAction.START_COUNTDOWN:
      clueState.countdownActive = true;
      clueState.countdownStartTime = this.clock.currentTime;
      if (countdownDurationSeconds) {
        clueState.countdownDurationSeconds = countdownDurationSeconds;
      }
      console.log(
        `Gamemaster action successful: Countdown started for clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}].`
      );
      break;
    case 'pauseCountdown':
    case GameMasterAction.PAUSE_COUNTDOWN:
      clueState.countdownActive = false;
      console.log(
        `Gamemaster action successful: Countdown paused for clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}].`
      );
      break;
    case 'resetCountdown':
    case GameMasterAction.RESET_COUNTDOWN:
      clueState.countdownStartTime = this.clock.currentTime;
      clueState.countdownActive = true;
      console.log(
        `Gamemaster action successful: Countdown reset for clue [${roundIdx}, ${currentCategoryIdx}, ${currentClueIdx}].`
      );
      break;
    default:
      console.warn(`Gamemaster action failed: Unknown action '${action}'.`);
  }
}

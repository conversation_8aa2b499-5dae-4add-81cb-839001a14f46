import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { GameMode, ClueState } from '../room_schema';
import { ServiceRegistry } from '../services';

export interface ResetQuestionPayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export function resetQuestionHandler(
  this: JeopardyRoom,
  client: { sessionId: string },
  payload: ResetQuestionPayload
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { roundIdx, categoryIdx, clueIdx } = payload;

  // 1. Validate user is host
  if (uid !== this.state.host) {
    console.warn('Reset question failed: Unauthorized attempt.');
    return;
  }

  // 2. Get the current clue state and data
  const roomState = this.state.gameState.roomState;
  if (!roomState) {
    console.warn('Reset question failed: Game not in progress.');
    return;
  }

  const clueState = roomState.roundStates?.[roundIdx]?.categoryStates?.[categoryIdx]?.clueStates?.[clueIdx];
  const clueData = this.state.roomData.rounds?.[roundIdx]?.categories?.[categoryIdx]?.clues?.[clueIdx];

  if (!clueState || !clueData) {
    console.error('Reset question failed: Clue not found at specified indices.');
    return;
  }

  // 3. Revert points if a player had answered it
  if (clueState.answeredByPlayerId) {
    const player = this.state.playerInfos.find(p => p.id === clueState.answeredByPlayerId);
    if (player) {
      let pointsToRevert = clueData.value;
      if (clueState.hintOpened) {
        pointsToRevert = Math.floor(pointsToRevert / 2);
      }
      player.score -= pointsToRevert;
      console.log(`Reverted ${pointsToRevert} points from player ${player.userName}. New score: ${player.score}.`);
    }
  }

  // 4. Reset the clue state
  const newClueState = new ClueState();
  roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[clueIdx] = newClueState;

  console.log(`Question [${roundIdx}, ${categoryIdx}, ${clueIdx}] has been reset.`);

  // 5. Persist the state
  ServiceRegistry.getInstance().database.saveRoom(this.state);
}

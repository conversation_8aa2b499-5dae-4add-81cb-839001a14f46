import { Client } from 'colyseus';
import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { ServiceRegistry } from '../services';
import { PlayerInfo } from '../room_schema';

export enum WaitingRoomAction {
  ADD_TEAM = 'ADD_TEAM',
  REMOVE_TEAM = 'REMOVE_TEAM',
  UPDATE_TEAM_NAME = 'UPDATE_TEAM_NAME',
}

export interface WaitingRoomPayload {
  action: WaitingRoomAction;
  teamId?: string;
  teamName?: string;
}

export function waitingRoomActionHandler(
  this: JeopardyRoom,
  client: Client,
  payload: WaitingRoomPayload
) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  const { action, teamId, teamName } = payload;

  console.log(
    `Waiting room action: Received action '${action}' from client ${uid}.`
  );

  // 1. Verify the client is the host
  if (uid !== this.state.host) {
    console.warn(
      `Waiting room action failed: Client ${uid} attempted an action but is not the host.`
    );
    return;
  }

  // 2. Verify the game is in the WaitingRoom state
  if (this.state.gameState.progressType !== 'WaitingRoom') {
    console.warn(
      `Waiting room action failed: Host ${uid} attempted an action, but the game has already started.`
    );
    return;
  }

  // 3. Perform the action
  switch (action) {
    case WaitingRoomAction.ADD_TEAM: {
      const newTeamId = `team_${this.state.playerInfos.length + 1}`;
      const newTeamName =
        teamName || `Team ${this.state.playerInfos.length + 1}`;
      const playerInfo = new PlayerInfo();
      playerInfo.id = newTeamId;
      playerInfo.userName = newTeamName;
      this.state.playerInfos.push(playerInfo);
      console.log(
        `Waiting room action successful: Host added team '${newTeamName}' with ID '${newTeamId}'.`
      );
      break;
    }
    case WaitingRoomAction.REMOVE_TEAM: {
      if (!teamId) {
        console.warn(
          'Waiting room action failed: No teamId provided for REMOVE_TEAM.'
        );
        return;
      }
      const teamIndex = this.state.playerInfos.findIndex(p => p.id === teamId);
      if (teamIndex > -1) {
        this.state.playerInfos.splice(teamIndex, 1);
        console.log(
          `Waiting room action successful: Host removed team with ID '${teamId}'.`
        );
      } else {
        console.warn(
          `Waiting room action failed: Could not find team with ID '${teamId}' to remove.`
        );
      }
      break;
    }
    case WaitingRoomAction.UPDATE_TEAM_NAME: {
      if (!teamId || !teamName) {
        console.warn(
          'Waiting room action failed: Missing teamId or teamName for UPDATE_TEAM_NAME.'
        );
        return;
      }
      const team = this.state.playerInfos.find(p => p.id === teamId);
      if (team) {
        team.userName = teamName;
        console.log(
          `Waiting room action successful: Host updated team ID '${teamId}' to name '${teamName}'.`
        );
      } else {
        console.warn(
          `Waiting room action failed: Could not find team with ID '${teamId}' to update.`
        );
      }
      break;
    }
    default:
      console.warn(`Waiting room action failed: Unknown action '${action}'.`);
  }

  // 4. Persist the state
  ServiceRegistry.getInstance().database.saveRoom(this.state);
  console.log('Room state saved after waiting room action.');
}

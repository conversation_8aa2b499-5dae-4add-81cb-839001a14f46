import { Client } from 'colyseus';
import { <PERSON><PERSON><PERSON>yRoom } from '../rooms/JeopardyRoom';
import { RoomStateProgress } from '../room_schema';

export function returnToBoardHandler(this: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, client: Client) {
  const uid = this.sessionIdToUidMap.get(client.sessionId);
  console.log(`Return to board request: Received from client ${uid}.`);

  // Only the host can perform this action
  if (uid !== this.state.host) {
    console.error(`Return to board failed: Client ${uid} is not the host.`);
    return;
  }

  // Game must be in the summary state
  if (this.state.gameState.progressType !== 'RoomSummary') {
    console.error(
      `Return to board failed: Game is not in summary state (current state: ${this.state.gameState.progressType}).`
    );
    return;
  }

  console.log(
    `Host ${uid} is returning to the game board. Restoring original state.`
  );

  // Restore the original game state by creating a new RoomStateProgress
  const originalState = this.state.gameState.roomSummary.originalState;
  const restoredState = new RoomStateProgress({
    roundIdx: originalState.roundIdx,
    currentPlayerIdx: originalState.currentPlayerIdx,
    roundStates: originalState.roundStates.clone()
  });

  this.state.gameState.roomState = restoredState;
  this.state.gameState.progressType = 'RoomState';

  // Clean up the summary state
  this.state.gameState.roomSummary = undefined;

  console.log('Successfully returned to RoomState.');
}

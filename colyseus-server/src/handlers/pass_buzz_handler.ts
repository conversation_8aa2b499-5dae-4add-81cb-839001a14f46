import { Client } from 'colyseus';
import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { checkAndAdvancePhase } from './clue_lifecycle_helpers';
import { LogType, PassLog } from '../structured_log';
import { addStructuredLogEntry } from './log_utils';

export interface PassBuzzPayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export function passBuzzHandler(
  this: JeopardyRoom,
  client: Client,
  payload: PassBuzzPayload
) {
  const { roundIdx, categoryIdx, clueIdx } = payload;
  const uid = this.sessionIdToUidMap.get(client.sessionId);

  if (!uid) {
    console.warn('passBuzz failed: Action by unknown client.');
    return;
  }

  const roomState = this.state.gameState.roomState;
  if (!roomState) {
    console.warn(`passBuzz failed: Action for inactive clue by player ${uid}.`);
    return;
  }

  const clueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];

  // Can't pass if you've already buzzed or the clue is done
  if (
    clueState.buzzedInPlayerQueue.includes(uid) ||
    (clueState.passedPlayers || []).includes(uid) ||
    clueState.clueComplete
  ) {
    return;
  }
  const player = this.state.playerInfos.find(p => p.id === uid);
  const logEntry = new PassLog();
  logEntry.assign({
    type: LogType.PASS,
    playerId: uid,
    playerName: player.userName
  });
  addStructuredLogEntry(this, clueState, logEntry);
  console.log(
    `Player ${uid} passed the buzz for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );
  if (!clueState.passedPlayers.includes(uid)) {
    clueState.passedPlayers.push(uid);
  }

  // Defer to the centralized helper to decide what to do next
  checkAndAdvancePhase(this, roundIdx, categoryIdx, clueIdx);
}

import { Jeop<PERSON>yRoom } from '../rooms/JeopardyRoom';
import { GameMode } from '../room_schema';
import { addStructuredLogEntry } from './log_utils';
import {
  AnswerIncorrectLog,
  HintOpenedLog,
  LogType,
  NoCorrectAnswerLog,
  TimerExpiredLog
} from '../structured_log';
import { ServiceRegistry } from '../services';

function getClueId(
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
): string {
  return `${roundIdx}-${categoryIdx}-${clueIdx}`;
}

/**
 * Centralized function to check if the current buzz-in phase should end.
 * This can happen if all players have acted (buzzed/passed) or if the timer runs out.
 * It then transitions the clue to the next state (hint phase or end).
 * @param room The JeopardyRoom instance.
 * @param roundIdx The index of the round.
 * @param categoryIdx The index of the category.
 * @param clueIdx The index of the clue.
 */
export function checkAndAdvancePhase(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  const clueState =
    room.state.gameState.roomState.roundStates[roundIdx].categoryStates[
      categoryIdx
    ].clueStates[clueIdx];

  if (clueState.clueComplete) {
    return;
  }

  const totalPlayers = room.state.playerIds.length;
  const playersBuzzed = clueState.buzzedInPlayerQueue.length;
  const playersPassed = clueState.passedPlayers.length;
  const playersActed = playersBuzzed + playersPassed;
  const buzzedQueueExhausted =
    clueState.queueAnswerTurnIdx >= playersBuzzed || playersBuzzed == 0;
  const buzzInTimerIsActive =
    clueState.buzzInTimerStartTime +
      room.state.roomSettings.buzzInTimerDurationMillis >
    room.clock.currentTime;

  // Condition 1: The queue of players who buzzed in is not yet exhausted.
  // We should wait for them to answer.
  if (!buzzedQueueExhausted) {
    console.log('Queue is not exhausted. Waiting for players to answer.');
    return;
  }

  // Condition 2: The queue is exhausted, but the buzz-in timer is still active
  // and not everyone has decided to buzz or pass. We should wait.
  if (buzzInTimerIsActive && playersActed < totalPlayers) {
    console.log('Buzz-in timer is active. Waiting for more players to act.');
    return;
  }

  // If we reach here, the buzz-in phase is over.
  // Either the timer expired, or everyone has made a choice.
  console.log(
    `Phase complete for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}]. Advancing state. Hint opened: ${clueState.hintOpened}`
  );
  if (
    room.state.roomSettings.gameMode === GameMode.BUZZER &&
    !clueState.hintOpened
  ) {
    startHintPhase(room, roundIdx, categoryIdx, clueIdx);
  } else {
    endClue(room, roundIdx, categoryIdx, clueIdx);
  }
}

/**
 * Processes the end of a player's turn due to an incorrect answer or timeout,
 * and advances the game to the next state (next player, hint phase, or end clue).
 * @param room The JeopardyRoom instance.
 * @param roundIdx The index of the round.
 * @param categoryIdx The index of the category.
 * @param clueIdx The index of the clue.
 * @param isTimeout A boolean indicating if the turn ended due to a timeout.
 */
export function deductPoints(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number,
  playerId: string,
  isTimeout: boolean
): number {
  const clueState =
    room.state.gameState.roomState.roundStates[roundIdx].categoryStates[
      categoryIdx
    ].clueStates[clueIdx];
  const clueData =
    room.state.roomData.rounds[roundIdx].categories[categoryIdx].clues[clueIdx];
  const player = room.state.playerInfos.find(p => p.id === playerId);

  let pointsToDeduct = 0;
  if (
    room.state.mode === GameMode.BUZZER ||
    (room.state.mode === GameMode.TURN_BASED && !isTimeout)
  ) {
    if (player) {
      pointsToDeduct = clueData.value;
      if (clueState.hintOpened) {
        pointsToDeduct /= 2;
      }
      player.score -= pointsToDeduct;
      const reason = isTimeout ? 'timeout' : 'incorrect answer';
      console.log(
        `Deducted ${pointsToDeduct} points from player ${player.userName} for ${reason}. New score: ${player.score}.`
      );
    }
  }
  return pointsToDeduct;
}

export function advanceTurn(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number,
  isTimeout: boolean
) {
  clearAnswerTimer(room, roundIdx, categoryIdx, clueIdx);

  const clueState =
    room.state.gameState.roomState.roundStates[roundIdx].categoryStates[
      categoryIdx
    ].clueStates[clueIdx];
  const currentPlayerId =
    clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];

  if (isTimeout) {
    const pointsToDeduct = deductPoints(
      room,
      roundIdx,
      categoryIdx,
      clueIdx,
      currentPlayerId,
      true
    );
    const player = room.state.playerInfos.find(p => p.id === currentPlayerId);
    const logEntry = new TimerExpiredLog();
    logEntry.assign({
      type: LogType.TIMER_EXPIRED,
      playerId: currentPlayerId,
      playerName: player.userName,
      pointsDeducted: pointsToDeduct
    });
    addStructuredLogEntry(room, clueState, logEntry);
  }

  clueState.queueAnswerTurnIdx++;

  // Check if there are more players in the queue
  if (clueState.queueAnswerTurnIdx < clueState.buzzedInPlayerQueue.length) {
    // Start the timer for the next player
    console.log(
      `Advancing to next player in queue: ${
        clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx]
      }`
    );
    startAnswerTimer(room, roundIdx, categoryIdx, clueIdx);
  } else {
    // The queue is exhausted, let the central function decide what's next.
    checkAndAdvancePhase(room, roundIdx, categoryIdx, clueIdx);
  }
}

/**
 * Starts the answer timer for the current player in the queue.
 * When the timer expires, it calls advanceTurn with the isTimeout flag.
 * @param room The JeopardyRoom instance.
 * @param roundIdx The index of the round.
 * @param categoryIdx The index of the category.
 * @param clueIdx The index of the clue.
 */
export function startAnswerTimer(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  clearAnswerTimer(room, roundIdx, categoryIdx, clueIdx); // Clear any existing timer for this clue

  const clueState =
    room.state.gameState.roomState.roundStates[roundIdx].categoryStates[
      categoryIdx
    ].clueStates[clueIdx];
  const currentPlayerId =
    clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];

  if (!currentPlayerId) {
    console.warn('Cannot start answer timer: no current player in the queue.');
    return;
  }

  clueState.answerStartTime = room.clock.currentTime;
  console.log(
    `Starting ${room.state.roomSettings.answerDurationMillis}ms answer timer for player ${currentPlayerId} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );

  const clueId = getClueId(roundIdx, categoryIdx, clueIdx);
  const timer = room.clock.setTimeout(
    () => advanceTurn(room, roundIdx, categoryIdx, clueIdx, true), // isTimeout = true
    room.state.roomSettings.answerDurationMillis
  );

  if (!room.answerTimers) {
    room.answerTimers = new Map();
  }
  room.answerTimers.set(clueId, timer);
}

/**
 * Clears any active answer timer for a given clue.
 * @param room The JeopardyRoom instance.
 * @param roundIdx The index of the round.
 * @param categoryIdx The index of the category.
 * @param clueIdx The index of the clue.
 */
export function clearAnswerTimer(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  const clueId = getClueId(roundIdx, categoryIdx, clueIdx);
  if (room.answerTimers && room.answerTimers.has(clueId)) {
    room.answerTimers.get(clueId).clear();
    room.answerTimers.delete(clueId);
  }
}

export function startHintPhase(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  const roomState = room.state.gameState.roomState;
  const clueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];

  if (clueState.clueComplete) {
    console.log('Clue is already complete. Not starting hint phase.');
    return;
  }

  const logEntry = new HintOpenedLog();
  logEntry.assign({
    type: LogType.HINT_OPENED
  });
  addStructuredLogEntry(room, clueState, logEntry);

  console.log(
    `Starting hint phase for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );
  clueState.hintOpened = true;

  // Reset buzzers and passes for the second phase
  clueState.buzzedInPlayerQueue.clear();
  clueState.passedPlayers.clear();
  clueState.queueAnswerTurnIdx = 0;

  clueState.buzzInTimerStartTime = room.clock.currentTime;
  console.log(
    `Buzzer mode (Phase 2): Starting buzz-in timer for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] for ${room.state.roomSettings.buzzInTimerDurationMillis}ms.`
  );
  room.clock.setTimeout(() => {
    console.log(
      `Buzz-in timer (Phase 2) expired for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
    );
    checkAndAdvancePhase(room, roundIdx, categoryIdx, clueIdx);
  }, room.state.roomSettings.buzzInTimerDurationMillis);
}

export function completeClue(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  const roomState = room.state.gameState.roomState;
  const clueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];
  if (clueState.clueComplete) {
    return;
  }

  clueState.clueComplete = true;
  roomState.roundStates[roundIdx].currentCategoryIdx = -1;
  roomState.roundStates[roundIdx].currentClueIdx = -1;
  ServiceRegistry.getInstance().database.saveRoom(room.state);
  console.log(
    `Clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] is now complete and saved to FireStore.`
  );
}

export function endClue(
  room: JeopardyRoom,
  roundIdx: number,
  categoryIdx: number,
  clueIdx: number
) {
  const roomState = room.state.gameState.roomState;
  const clueState =
    roomState.roundStates[roundIdx].categoryStates[categoryIdx].clueStates[
      clueIdx
    ];

  if (clueState.showAnswer) {
    return;
  }

  if (!clueState.answeredByPlayerId) {
    const logEntry = new NoCorrectAnswerLog();
    logEntry.assign({
      type: LogType.NO_CORRECT_ANSWER
    });
    addStructuredLogEntry(room, clueState, logEntry);
  }

  console.log(
    `Ending clue [${roundIdx}, ${categoryIdx}, ${clueIdx}]. Showing answer for ${room.state.roomSettings.showAnswerDurationMillis}ms.`
  );
  if (room.state.roomSettings.gameMode === GameMode.TURN_BASED) {
    console.log(
      `Turn-based mode: Advancing to next player. Current player index: ${roomState.currentPlayerIdx}.`
    );
    roomState.currentPlayerIdx =
      (roomState.currentPlayerIdx + 1) % room.state.playerIds.length;
  }
  clueState.showAnswer = true;
  clueState.showAnswerStartTime = room.clock.currentTime;
  room.clock.setTimeout(() => {
    completeClue(room, roundIdx, categoryIdx, clueIdx);
  }, room.state.roomSettings.showAnswerDurationMillis);
}

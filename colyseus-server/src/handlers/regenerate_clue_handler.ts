import { Client } from 'colyseus';
import { ArraySchema } from '@colyseus/schema';
import { JeopardyRoom } from '../rooms/JeopardyRoom';
import { GameMode, Clue } from '../room_schema';
import { generateSingleClue } from '../round_generator';

export interface RegenerateCluePayload {
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  instructions?: string;
}

export async function regenerateClueHandler(
  this: <PERSON>op<PERSON>yRoom,
  client: Client,
  payload: RegenerateCluePayload
) {
  const { roundIdx, categoryIdx, clueIdx, instructions } = payload;
  const uid = this.sessionIdToUidMap.get(client.sessionId);

  console.log(
    `Regenerate clue request from client ${uid} for clue [${roundIdx}, ${categoryIdx}, ${clueIdx}].`
  );
  if (instructions) {
    console.log(`Instructions provided: "${instructions}"`);
  }

  // 1. Validate user is host and in Gamemaster mode
  if (uid !== this.state.host || this.state.mode !== GameMode.GAMEMASTER) {
    console.warn('Regenerate clue failed: Unauthorized attempt.');
    return;
  }

  const roomData = this.state.roomData;
  const category = roomData.rounds[roundIdx]?.categories[categoryIdx];
  if (!category) {
    console.error('Regenerate clue failed: Category not found.');
    return;
  }

  try {
    // 2. Generate a new clue using the LlmService
    console.log(
      `Generating new clue for category "${category.categoryTitle}"...`
    );
    const newClueData = await generateSingleClue(
      category.categoryTitle,
      instructions,
      this.state.roomSettings.llmAgent
    );
    console.log('New clue data received from LLM service.');

    // 3. Update the clue in roomData
    const oldClue = category.clues[clueIdx];
    const newClue = new Clue();
    newClue.questionHTML = newClueData.questionHTML || '';
    newClue.answer = newClueData.answer || '';
    newClue.hint = newClueData.hint || '';
    newClue.detailedFactsAboutAnswer = new ArraySchema<string>(
      ...(newClueData.detailedFactsAboutAnswer || [])
    );
    newClue.value = oldClue.value; // Keep the original point value
    category.clues[clueIdx] = newClue;
    console.log('New Clue: ', newClue.toJSON());

    // 4. Reset the corresponding clue state
    const clueState = this.state.gameState.roomState.roundStates[roundIdx]
      ?.categoryStates[categoryIdx]?.clueStates[clueIdx];
    if (clueState) {
      clueState.hintOpened = false;
      clueState.showAnswer = false;
      clueState.clueComplete = false;
      clueState.buzzedInPlayerQueue.clear();
      clueState.passedPlayers.clear();
      console.log(
        `Clue state for [${roundIdx}, ${categoryIdx}, ${clueIdx}] has been reset.`
      );
    }

    console.log(
      `Clue [${roundIdx}, ${categoryIdx}, ${clueIdx}] regenerated successfully.`
    );
  } catch (error) {
    console.error('Error regenerating clue:', error);
  }
}

import {
  Room,
  Round,
  GameMode as LegacyGameMode,
  PlayerInfo as LegacyPlayerInfo,
  RoomSettings as LegacyRoomSettings,
  FullRoomState as LegacyFullRoomState,
  GameProgress as LegacyGameProgress,
  WaitingRoom as LegacyWaitingRoom,
  GeneratingQuestions as LegacyGeneratingQuestions,
  RoomState as LegacyRoomState,
  RoomSummary as LegacyRoomSummary,
  RoundState as LegacyRoundState,
  CategoryState as LegacyCategoryState,
  ClueState as LegacyClueState,
  AnswerExplanation as LegacyAnswerExplanation,
  StructuredClueLog as LegacyStructuredClueLog,
  LogEntry as LegacyLogEntry,
  LogType as LegacyLogType,
  ClueSelectedLog as LegacyClueSelectedLog,
  BuzzInLog as LegacyBuzzInLog,
  AnswerCorrectLog as LegacyAnswerCorrectLog,
  AnswerIncorrectLog as LegacyAnswerIncorrectLog,
  PassLog as LegacyPassLog,
  TimerExpiredLog as LegacyTimerExpiredLog,
  HintOpenedLog as LegacyHintOpenedLog,
  NoCorrectAnswerLog as LegacyNoCorrectAnswerLog,
  WaitingForAnswerLog as LegacyWaitingForAnswerLog,
  ShowAnswerLog as LegacyShowAnswerLog,
  PointsAssignedLog as LegacyPointsAssignedLog
} from '../../functions/src/resources';
import { Room as ColyseusRoom } from 'colyseus.js';
import {
  Room as ColyseusRoomState,
  GameMode as ColyseusGameMode,
  PlayerInfo as ColyseusPlayerInfo,
  RoomSettings as ColyseusRoomSettings,
  FullRoomState as ColyseusFullRoomState,
  WaitingRoom as ColyseusWaitingRoom,
  GeneratingQuestions as ColyseusGeneratingQuestions,
  RoomState as ColyseusRoomStateProgress,
  RoomSummary as ColyseusRoomSummary,
  RoundState as ColyseusRoundState,
  CategoryState as ColyseusCategoryState,
  ClueState as ColyseusClueState,
  AnswerExplanation as ColyseusAnswerExplanation,
  Category as ColyseusCategory,
  Clue as ColyseusClue
} from './room_schema';
import {
  BaseLogEntry,
  StructuredClueLog,
  ClueSelectedLog as ColyseusClueSelectedLog,
  BuzzInLog as ColyseusBuzzInLog,
  AnswerCorrectLog as ColyseusAnswerCorrectLog,
  AnswerIncorrectLog as ColyseusAnswerIncorrectLog,
  PassLog as ColyseusPassLog,
  TimerExpiredLog as ColyseusTimerExpiredLog,
  WaitingForAnswerLog as ColyseusWaitingForAnswerLog,
  ShowAnswerLog as ColyseusShowAnswerLog,
  PointsAssignedLog as ColyseusPointsAssignedLog
} from './structured_log';

export function adaptColyseusRoomToLegacy(
  room: ColyseusRoom<ColyseusRoomState>
): Room {
  const state = room.state;
  console.log('Adapting room state:', state.toJSON());

  const res: Room = {
    roomId: room.roomId,
    host: state.host,
    mode: adaptGameMode(state.mode),
    createdAt: state.createdAt,
    playerIds: Array.from(state.playerIds || []),
    playerInfos: Array.from(state.playerInfos || []).map(p =>
      adaptPlayerInfo(p)
    ),
    categoryTitles: Array.from(state.categoryTitles || []),
    gameState: state.gameState
      ? adaptFullRoomState(state.gameState)
      : { gameProgress: { type: 'WaitingRoom' } },
    roomSettings: state.roomSettings
      ? adaptRoomSettings(state.roomSettings)
      : undefined,
    roomData: state.roomData ? adaptRoomData(state.roomData) : undefined
  };

  console.log('Adapted room state:', res);
  return res;
}

function adaptRoomData(roomData: any): any {
  if (!roomData) return undefined;
  return {
    rounds: Array.from(roomData.rounds).map((round: any) => ({
      categories: Array.from(round.categories).map((category: any) => ({
        categoryTitle: category.categoryTitle,
        categoryDescription: category.categoryDescription,
        clues: (Array.from(category.clues) as ColyseusClue[]).map(
          (clue: ColyseusClue) => {
            return {
              value: clue.value,
              questionSentences: Array.from(clue.questionSentences),
              questionHTML: clue.questionHTML,
              hint: clue.hint,
              hintBlanks: clue.hintBlanks,
              answer: clue.answer,
              detailedFactsAboutAnswer: Array.from(
                clue.detailedFactsAboutAnswer
              )
            };
          }
        )
      }))
    }))
  };
}

function adaptGameMode(mode: ColyseusGameMode): LegacyGameMode {
  switch (mode) {
    case ColyseusGameMode.BUZZER:
      return LegacyGameMode.BUZZER;
    case ColyseusGameMode.GAMEMASTER:
      return LegacyGameMode.GAMEMASTER;
    case ColyseusGameMode.TURN_BASED:
      return LegacyGameMode.TURN_BASED;
    default:
      return LegacyGameMode.BUZZER; // Default case
  }
}

function adaptPlayerInfo(playerInfo: ColyseusPlayerInfo): LegacyPlayerInfo {
  return {
    score: playerInfo.score,
    userName: playerInfo.userName,
    photoUrl: playerInfo.photoUrl
  };
}

function adaptRoomSettings(settings: ColyseusRoomSettings): LegacyRoomSettings {
  return {
    gameMode: adaptGameMode(settings.gameMode as ColyseusGameMode),
    buzzInTimerDurationMillis: settings.buzzInTimerDurationMillis,
    answerDurationMillis: settings.answerDurationMillis,
    showAnswerDurationMillis: settings.showAnswerDurationMillis,
    initialThinkingDurationMillis: 0, // This is correct, legacy client doesn't use it
    llmAgent: {
      provider: settings.llmAgent.provider,
      modelName: settings.llmAgent.modelName
    },
    numQuestions: settings.numQuestions,
    generationStrategy: settings.generationStrategy
  };
}

function adaptFullRoomState(
  fullRoomState: ColyseusFullRoomState
): LegacyFullRoomState {
  const gameProgress = adaptGameProgress(fullRoomState);
  return {
    gameProgress
  };
}

function adaptGameProgress(
  fullRoomState: ColyseusFullRoomState
): LegacyGameProgress {
  switch (fullRoomState.progressType) {
    case 'WaitingRoom':
      return { type: 'WaitingRoom' } as LegacyWaitingRoom;
    case 'GeneratingQuestions':
      const gq = fullRoomState.generatingQuestions as ColyseusGeneratingQuestions;
      if (!gq) {
        return {
          type: 'GeneratingQuestions',
          progress: 0,
          message: '',
          category: '',
          questionIndex: 0,
          startTime: 0,
          streamedResponse: '',
          questionsGenerated: [],
        } as LegacyGeneratingQuestions;
      }
      return {
        type: 'GeneratingQuestions',
        progress: gq.progress,
        message: gq.message,
        category: gq.category,
        questionIndex: gq.questionIndex,
        startTime: gq.startTime,
        streamedResponse: gq.streamedResponse,
        questionsGenerated: Array.from(gq.questionsGenerated || [])
      } as LegacyGeneratingQuestions;
    case 'RoomState':
      const rs = fullRoomState.roomState as ColyseusRoomStateProgress;
      if (!rs) {
        return { type: 'WaitingRoom' } as LegacyWaitingRoom; // Fallback
      }
      return adaptRoomState(rs);
    case 'RoomSummary':
      const summary = fullRoomState.roomSummary as ColyseusRoomSummary;
      if (!summary) {
        return { type: 'WaitingRoom' } as LegacyWaitingRoom; // Fallback
      }
      return {
        type: 'RoomSummary',
        originalState: summary.originalState
          ? adaptRoomState(summary.originalState)
          : undefined,
        memorableQuestions: Array.from(summary.memorableQuestions || []).map(mq => ({
          category: mq.category,
          question: mq.question,
          answer: mq.answer,
          value: mq.value,
          reason: mq.reason
        }))
      } as LegacyRoomSummary;
    default:
      throw new Error(
        `Unknown game progress type: ${fullRoomState.progressType}`
      );
  }
}

function adaptRoomState(roomState: ColyseusRoomStateProgress): LegacyRoomState {
  return {
    type: 'RoomState',
    roundIdx: roomState.roundIdx,
    currentPlayerIdx: roomState.currentPlayerIdx,
    roundStates: Array.from(roomState.roundStates).map(rs =>
      adaptRoundState(rs)
    )
  };
}

function adaptRoundState(roundState: ColyseusRoundState): LegacyRoundState {
  return {
    currentCategoryIdx: roundState.currentCategoryIdx,
    currentClueIdx: roundState.currentClueIdx,
    categoryStates: Array.from(roundState.categoryStates).map(cs =>
      adaptCategoryState(cs)
    )
  };
}

function adaptCategoryState(
  categoryState: ColyseusCategoryState
): LegacyCategoryState {
  return {
    clueStates: Array.from(categoryState.clueStates).map(cs =>
      adaptClueState(cs)
    )
  };
}

function adaptClueState(clueState: ColyseusClueState): LegacyClueState {
  return {
    hintOpened: clueState.hintOpened,
    showAnswer: clueState.showAnswer,
    buzzInTimerStartTime: clueState.buzzInTimerStartTime,
    buzzedInPlayerQueue: Array.from(clueState.buzzedInPlayerQueue || []),
    passedPlayers: Array.from(clueState.passedPlayers || []),
    queueAnswerTurnIdx: clueState.queueAnswerTurnIdx,
    answerStartTime: clueState.answerStartTime,
    showAnswerStartTime: clueState.showAnswerStartTime,
    skipShowAnswer: Array.from(clueState.skipShowAnswer || []),
    clueComplete: clueState.clueComplete,
    structuredClueLog: adaptStructuredClueLog(clueState.structuredClueLog),
    answerExplanations: Array.from(clueState.answerExplanations || []).map(ae =>
      adaptAnswerExplanation(ae)
    ),
    answeredByPlayerId: clueState.answeredByPlayerId,
    countdownStartTime: clueState.countdownStartTime,
    countdownDurationSeconds: clueState.countdownDurationSeconds,
    countdownActive: clueState.countdownActive
  };
}

function adaptStructuredClueLog(
  log: StructuredClueLog
): LegacyStructuredClueLog {
  if (!log) {
    return { entries: [] };
  }
  return {
    entries: Array.from(log.entries || []).map(e => adaptLogEntry(e))
  };
}

function adaptLogEntry(entry: BaseLogEntry): LegacyLogEntry {
  const legacyType = entry.type as LegacyLogType;

  switch (legacyType) {
    case LegacyLogType.CLUE_SELECTED:
      const cs = entry as ColyseusClueSelectedLog;
      return {
        type: LegacyLogType.CLUE_SELECTED,
        timestamp: cs.timestamp,
        snarkyComment: cs.snarkyComment,
        playerId: cs.playerId,
        playerName: cs.playerName,
        categoryTitle: cs.categoryTitle,
        clueValue: cs.clueValue
      };
    case LegacyLogType.BUZZ_IN:
      const bi = entry as ColyseusBuzzInLog;
      return {
        type: LegacyLogType.BUZZ_IN,
        timestamp: bi.timestamp,
        snarkyComment: bi.snarkyComment,
        playerId: bi.playerId,
        playerName: bi.playerName,
        timeTaken: bi.timeTaken
      };
    case LegacyLogType.ANSWER_CORRECT:
      const ac = entry as ColyseusAnswerCorrectLog;
      return {
        type: LegacyLogType.ANSWER_CORRECT,
        timestamp: ac.timestamp,
        snarkyComment: ac.snarkyComment,
        playerId: ac.playerId,
        playerName: ac.playerName,
        guess: ac.guess,
        pointsAwarded: ac.pointsAwarded
      };
    case LegacyLogType.ANSWER_INCORRECT:
      const ai = entry as ColyseusAnswerIncorrectLog;
      return {
        type: LegacyLogType.ANSWER_INCORRECT,
        timestamp: ai.timestamp,
        snarkyComment: ai.snarkyComment,
        playerId: ai.playerId,
        playerName: ai.playerName,
        guess: ai.guess,
        pointsDeducted: ai.pointsDeducted
      };
    case LegacyLogType.PASS:
      const p = entry as ColyseusPassLog;
      return {
        type: LegacyLogType.PASS,
        timestamp: p.timestamp,
        snarkyComment: p.snarkyComment,
        playerId: p.playerId,
        playerName: p.playerName,
        pointsDeducted: p.pointsDeducted
      };
    case LegacyLogType.TIMER_EXPIRED:
      const te = entry as ColyseusTimerExpiredLog;
      return {
        type: LegacyLogType.TIMER_EXPIRED,
        timestamp: te.timestamp,
        snarkyComment: te.snarkyComment,
        playerId: te.playerId,
        playerName: te.playerName,
        pointsDeducted: te.pointsDeducted
      };
    case LegacyLogType.HINT_OPENED:
      return {
        type: LegacyLogType.HINT_OPENED,
        timestamp: entry.timestamp,
        snarkyComment: entry.snarkyComment
      };
    case LegacyLogType.NO_CORRECT_ANSWER:
      return {
        type: LegacyLogType.NO_CORRECT_ANSWER,
        timestamp: entry.timestamp,
        snarkyComment: entry.snarkyComment
      };
    case LegacyLogType.WAITING_FOR_ANSWER:
      const wfa = entry as ColyseusWaitingForAnswerLog;
      return {
        type: LegacyLogType.WAITING_FOR_ANSWER,
        timestamp: wfa.timestamp,
        snarkyComment: wfa.snarkyComment,
        playerId: wfa.playerId,
        playerName: wfa.playerName
      };
    case LegacyLogType.SHOW_ANSWER:
      const sa = entry as ColyseusShowAnswerLog;
      return {
        type: LegacyLogType.SHOW_ANSWER,
        timestamp: sa.timestamp,
        snarkyComment: sa.snarkyComment,
        answer: sa.answer
      };
    case LegacyLogType.POINTS_ASSIGNED:
      const pa = entry as ColyseusPointsAssignedLog;
      return {
        type: LegacyLogType.POINTS_ASSIGNED,
        timestamp: pa.timestamp,
        snarkyComment: pa.snarkyComment,
        playerId: pa.playerId,
        playerName: pa.playerName,
        teamName: pa.teamName,
        pointsAwarded: pa.pointsAwarded
      };
    default:
      // This should never happen, but it's a good practice to have a default case.
      throw new Error(`Unknown log type: ${legacyType}`);
  }
}

function adaptAnswerExplanation(
  explanation: ColyseusAnswerExplanation
): LegacyAnswerExplanation {
  return {
    isCorrect: explanation.isCorrect,
    explanation: explanation.explanation
  };
}

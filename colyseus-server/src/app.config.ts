import config from '@colyseus/tools';
import { monitor } from '@colyseus/monitor';
import { playground } from '@colyseus/playground';
import cors from 'cors';
import express from 'express';

/**
 * Import your Room files
 */
import { JeopardyRoom } from './rooms/JeopardyRoom';
import { IRoomCache, matchMaker } from 'colyseus';
import { FirestoreDatabase } from './firestore_database';
import { ServiceRegistry } from './services';

// Define allowed origins for CORS
// localhost for dev, jeopardy-gpt for prod.

const allowedOrigins =
  process.env.PRODUCTION === 'true'
    ? ['https://jeopardy-gpt.web.app']
    : ['https://localhost:4200', 'http://localhost:4200'];

const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    if (allowedOrigins.indexOf(origin) === -1) {
      const msg =
        'The CORS policy for this site does not allow access from the specified Origin.';
      return callback(new Error(msg), false);
    }
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

export default config({
  initializeGameServer: gameServer => {
    /**
     * Define your room handlers:
     */
    gameServer.define('jeopardy_room', JeopardyRoom);
  },

  initializeExpress: app => {
    // Add the express.json() middleware to parse JSON request bodies
    app.use(express.json());

    // Use the configured CORS options
    app.use(cors(corsOptions));
    app.options('*', cors(corsOptions)); // Enable pre-flight for all routes

    /**
     * Bind your custom express routes here:
     * Read more: https://expressjs.com/en/starter/basic-routing.html
     */
    app.get('/hello_world', (req, res) => {
      res.send("It's time to kick ass and chew bubblegum!");
    });
    app.post('/hello_world', (req, res) => {
      res.send("It's time to kick ass and chew bubblegum!");
    });

    // 1. Create a new room -> getOrCreate, without roomId.
    // 2. Join a room that's in-memory -> getOrCreate, with roomId.
    // 3. Join a room in DB. -> getOrCreate, with roomId.
    // 4. Join a non-existent room. -> getOrCreate, with roomId that doesn't exist.
    app.post('/getOrCreate/:roomName', async (req, res) => {
      console.log('/getOrCreate/:roomName called with body:', req.body);
      const { roomName } = req.params;
      const { roomId, ...clientOptions } = req.body;

      try {
        let room: IRoomCache;

        if (roomId) {
          console.log(`Attempting to get or create room '${roomId}'...`);
          // Check if the room is already active in memory
          room = await matchMaker.getRoomById(roomId);

          if (!room) {
            console.log(
              `Room ${roomId} not found in memory. Checking database...`
            );
            // If not in memory, try to find it in the database
            const roomDataFromDB = await ServiceRegistry.getInstance().database.loadRoom(
              roomId
            );

            if (roomDataFromDB) {
              console.log(
                `Room ${roomId} found in database. Restoring room...`
              );
              // If found, create the room and restore its state
              room = await matchMaker.createRoom(roomName, {
                roomId,
                roomDataFromDB,
                ...clientOptions
              });
            } else {
              if (clientOptions.isSpectator) {
                console.log(
                  `Room ${roomId} not found in database. Spectators cannot join non-existent rooms.`
                );
                throw new Error('Spectator cannot join a non-existent room.');
              }
              console.log(`Room ${roomId} not found. Creating new room...`);
              // Don't throw an error, let it fall through to the creation logic below
              clientOptions.roomId = roomId;
            }
          }
        }

        if (!room) {
          // If roomId was not provided or the room didn't exist, create a new one.
          if (!clientOptions.roomId) {
            // Create a 6 character upper-case room id if one isn't already set.
            clientOptions.roomId = Math.random()
              .toString(36)
              .substring(2, 8)
              .toUpperCase();
          }

          console.log(
            `Creating new room with ${roomName}, ${JSON.stringify(
              clientOptions
            )}`
          );
          // Check if the client is Authed.
          if (ServiceRegistry.getInstance().auth) {
            console.log('Authenticating client...');
            const user = await ServiceRegistry.getInstance().auth.verifyIdToken(
              clientOptions.token
            );
            console.log('Client authenticated successfully as user', user.uid);
          }
          // If no room was found or restored, create a new one
          room = await matchMaker.createRoom(roomName, clientOptions);
          console.log(`Finished creating new room ${room.roomId}.`);
        }

        // Reserve a seat for the client
        const seatReservation = await matchMaker.reserveSeatFor(
          room,
          clientOptions
        );
        res.json(seatReservation);
      } catch (e) {
        res.status(500).json({ error: (e as Error).message });
      }
    });

    /**
     * Use @colyseus/playground
     * (It is not recommended to expose this route in a production environment)
     */
    if (process.env.NODE_ENV !== 'production') {
      app.use('/', playground());
    }

    /**
     * Use @colyseus/monitor
     * It is recommended to protect this route with a password
     * Read more: https://docs.colyseus.io/tools/monitor/#restrict-access-to-the-panel-using-a-password
     */
    app.use('/monitor', monitor());
  },

  beforeListen: () => {
    /**
     * Before before gameServer.listen() is called.
     */
  }
});

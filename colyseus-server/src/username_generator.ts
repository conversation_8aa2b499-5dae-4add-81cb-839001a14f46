const adjectives: string[] = [
  'Agile', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>lly', '<PERSON><PERSON>y',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>lucky', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'
];

const animals: string[] = [
  '<PERSON>ardvark', '<PERSON><PERSON>', 'Cheetah', 'Dingo', 'Elephant', '<PERSON>', '<PERSON>az<PERSON>',
  '<PERSON><PERSON>', 'Iguana', '<PERSON>', '<PERSON>ala', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>uokka', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Urial',
  'Vulture', 'Walrus', 'Xerus', 'Yak', 'Zebra'
];

export function generateUsername(): string {
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const animal = animals[Math.floor(Math.random() * animals.length)];
  const number = Math.floor(Math.random() * 90) + 10; // Two-digit number

  return `${adjective}${animal}${number}`;
}

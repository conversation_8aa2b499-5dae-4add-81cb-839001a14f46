// Colyseus Room Schema for Jeopardy Game (moved from functions/src/colyseus_room_schema.ts)
// This schema mirrors the Room/GameState/CategoryState/ClueState structure used by the client
// and is intended to be used with Colyseus' @colyseus/schema for real-time state sync.

import { Schema, type, view, ArraySchema } from '@colyseus/schema';
import { StructuredClueLog } from './structured_log';

export { ArraySchema };

// --- ENUMS & SIMPLE TYPES ---
export enum GameMode {
  TURN_BASED = 1,
  BUZZER = 2,
  GAMEMASTER = 3
}

export enum GenerationStrategy {
  DETAILED_WITH_HINTS = 1,
  SHORT_AND_SWEET = 2,
}

export class LlmAgent extends Schema {
  @type('string') provider:
    | 'OpenAI'
    | 'Gemini'
    | 'DeepSeekAI'
    | 'Fake'
    | 'FakeReal' = 'Gemini';
  @type('string') modelName = 'gemini-2.5-flash';
  constructor(data: Partial<LlmAgent> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class AnswerExplanation extends Schema {
  @type('boolean') isCorrect = false;
  @type('string') explanation = '';
  constructor(data: Partial<AnswerExplanation> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class ChatMessage extends Schema {
  @type('string') senderId = '';
  @type('string') senderName = '';
  @type('string') content = '';
  @type('number') timestamp = 0;

  constructor(data: Partial<ChatMessage> = {}) {
    super();
    Object.assign(this, data);
  }
}

// --- CLUE/QUESTION ---
export class Clue extends Schema {
  @type('number') value = 0;
  // These are visible to players only when this is the active clue.
  // Always visible to the Game Master.
  @type(['string']) questionSentences = new ArraySchema<string>();
  @type('string') questionHTML = '';
  @type('string') hint = '';
  @type('string') hintBlanks = '';
  @type('string') answer = '';
  @type(['string']) detailedFactsAboutAnswer = new ArraySchema<string>();
  constructor(data: Partial<Clue> = {}) {
    super();
    if (data.questionSentences) {
      this.questionSentences = new ArraySchema<string>();
      data.questionSentences.forEach(q => this.questionSentences.push(q));
      delete data.questionSentences;
    }
    if (data.detailedFactsAboutAnswer) {
      this.detailedFactsAboutAnswer = new ArraySchema<string>();
      data.detailedFactsAboutAnswer.forEach(q =>
        this.detailedFactsAboutAnswer.push(q)
      );
      delete data.detailedFactsAboutAnswer;
    }
    Object.assign(this, data);
  }
}

// --- CATEGORY/ROUND ---
export class Category extends Schema {
  @type('string') categoryTitle = '';
  @type('string') categoryDescription = '';
  @type([Clue]) clues = new ArraySchema<Clue>();
  constructor(data: Partial<Category> = {}) {
    super();
    if (data.clues) {
      this.clues = new ArraySchema<Clue>();
      data.clues.forEach(c => this.clues.push(new Clue(c)));
      delete data.clues;
    }
    Object.assign(this, data);
  }
}

export class ClueState extends Schema {
  @type('boolean') hintOpened = false;
  @type('boolean') showAnswer = false;
  @type('number') buzzInTimerStartTime = 0;
  @type(['string']) buzzedInPlayerQueue = new ArraySchema<string>();
  @type(['string']) passedPlayers = new ArraySchema<string>();
  @type('number') queueAnswerTurnIdx = -1;
  @type('number') answerStartTime = 0;
  @type('number') showAnswerStartTime = 0;
  @type(['string']) skipShowAnswer = new ArraySchema<string>();
  @type('boolean') clueComplete = false;
  @type(StructuredClueLog) structuredClueLog = new StructuredClueLog();
  @type([AnswerExplanation]) answerExplanations = new ArraySchema<
    AnswerExplanation
  >();
  @type('string') answeredByPlayerId = '';
  @type('number') countdownStartTime = 0;
  @type('number') countdownDurationSeconds = 0;
  @type('boolean') countdownActive = false;

  constructor(data: Partial<ClueState> = {}) {
    super();
    if (data.answerExplanations) {
      this.answerExplanations = new ArraySchema<AnswerExplanation>();
      data.answerExplanations.forEach(ae =>
        this.answerExplanations.push(new AnswerExplanation(ae))
      );
      delete data.answerExplanations;
    }
    if (data.structuredClueLog) {
      this.structuredClueLog = new StructuredClueLog(data.structuredClueLog);
      delete data.structuredClueLog;
    }
    Object.assign(this, data);
  }
}

export class CategoryState extends Schema {
  @type([ClueState]) clueStates = new ArraySchema<ClueState>();
  constructor(data: Partial<CategoryState> = {}) {
    super();
    if (data.clueStates) {
      this.clueStates = new ArraySchema<ClueState>();
      data.clueStates.forEach(cs => this.clueStates.push(new ClueState(cs)));
      delete data.clueStates;
    }
    Object.assign(this, data);
  }
}

export class RoundState extends Schema {
  @type('number') currentCategoryIdx = -1;
  @type('number') currentClueIdx = -1;
  @type([CategoryState]) categoryStates = new ArraySchema<CategoryState>();
  constructor(data: Partial<RoundState> = {}) {
    super();
    if (data.categoryStates) {
      this.categoryStates = new ArraySchema<CategoryState>();
      data.categoryStates.forEach(cs =>
        this.categoryStates.push(new CategoryState(cs))
      );
      delete data.categoryStates;
    }
    Object.assign(this, data);
  }
}

// --- PLAYER ---
export class PlayerInfo extends Schema {
  @type('string') id = ''; // Colyseus sessionId or Firebase UID
  @type('string') userName = '';
  @type('string') displayName = '';
  @type('string') email = '';
  @type('number') score = 0;
  @type('string') photoUrl?: string;

  constructor(data: Partial<PlayerInfo> = {}) {
    super();
    Object.assign(this, data);
  }
}

// --- ROOM SETTINGS ---
export class RoomSettings extends Schema {
  @type('number') gameMode = GameMode.TURN_BASED;
  @type('number') buzzInTimerDurationMillis =
    process.env['PRODUCTION'] === 'true' ? 60000 : 2000;
  @type('number') answerDurationMillis =
    process.env['PRODUCTION'] === 'true' ? 60000 : 2000;
  @type('number') showAnswerDurationMillis =
    process.env['PRODUCTION'] === 'true' ? 10000 : 2000;
  @type(LlmAgent) llmAgent = new LlmAgent();
  @type('number') numQuestions = 5;
  @type('number') generationStrategy = GenerationStrategy.SHORT_AND_SWEET;

  constructor(data: Partial<RoomSettings> = {}) {
    super();
    if (data.llmAgent) {
      this.llmAgent = new LlmAgent(data.llmAgent);
      delete data.llmAgent;
    }
    Object.assign(this, data);
  }
}

// --- GAME PROGRESS UNION ---
export class WaitingRoom extends Schema {
  @type('string') type = 'WaitingRoom';
  constructor(data: Partial<WaitingRoom> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class ActiveClue extends Schema {
  @type('number') roundIdx = -1;
  @type('number') categoryIdx = -1;
  @type('number') clueIdx = -1;
  constructor(data: Partial<ActiveClue> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class GeneratingQuestions extends Schema {
  @type('string') type = 'GeneratingQuestions';
  @type('number') progress = 0;
  @type('string') message = '';
  @type('string') category = '';
  @type('number') questionIndex = 0;
  @type('number') startTime = 0;
  @type('string') streamedResponse = '';
  @type(['number']) questionsGenerated = new ArraySchema<number>();
  constructor(data: Partial<GeneratingQuestions> = {}) {
    super();
    Object.assign(this, data);
  }
}

// --- MEMORABLE QUESTION ---
export class MemorableQuestion extends Schema {
  @type('string') category = '';
  @type('string') question = '';
  @type('string') answer = '';
  @type('number') value = 0;
  @type('string') reason = '';
  constructor(data: Partial<MemorableQuestion> = {}) {
    super();
    Object.assign(this, data);
  }
}

// --- ROOM STATE (for RoomSummary/originalState and RoomStateProgress) ---
export class RoomState extends Schema {
  @type('string') type = 'RoomState';
  @type('number') roundIdx = 0;
  @type([RoundState]) roundStates = new ArraySchema<RoundState>();
  @type('number') currentPlayerIdx = 0;
  constructor(data: Partial<RoomState> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class RoomSummary extends Schema {
  @type('string') type = 'RoomSummary';
  @type(RoomState) originalState = new RoomState();
  @type([MemorableQuestion]) memorableQuestions = new ArraySchema<
    MemorableQuestion
  >();
  constructor(data: Partial<RoomSummary> = {}) {
    super();
    Object.assign(this, data);
  }
}

export class RoomStateProgress extends Schema {
  @type('string') type = 'RoomState';
  @type('number') roundIdx = 0;
  @type([RoundState]) roundStates = new ArraySchema<RoundState>();
  @type('number') currentPlayerIdx = 0;
  constructor(data: Partial<RoomStateProgress> = {}) {
    super();
    if (data.roundStates) {
      this.roundStates = new ArraySchema<RoundState>();
      data.roundStates.forEach(rs => this.roundStates.push(new RoundState(rs)));
      delete data.roundStates;
    }
    Object.assign(this, data);
  }
}

// --- FULL ROOM STATE ---
export class FullRoomState extends Schema {
  // Discriminated union for gameProgress
  @type('string') progressType = 'WaitingRoom'; // "WaitingRoom" | "RoomState" | "RoomSummary" | "GeneratingQuestions"
  @type(WaitingRoom) waitingRoom?: WaitingRoom;
  @type(RoomStateProgress) roomState?: RoomStateProgress;
  @type(RoomSummary) roomSummary?: RoomSummary;
  @type(GeneratingQuestions) generatingQuestions?: GeneratingQuestions;
  @type(ActiveClue) activeClue = new ActiveClue();
  @type('boolean') playerCanBuzz = false;
  @type('number') buzzTimerDuration = 0;

  constructor(data: Partial<FullRoomState> = {}) {
    super();
    if (data.activeClue) {
      this.activeClue = new ActiveClue(data.activeClue);
      delete data.activeClue;
    }
    if (data.waitingRoom) {
      this.waitingRoom = new WaitingRoom(data.waitingRoom);
      delete data.waitingRoom;
    }
    if (data.roomState) {
      this.roomState = new RoomStateProgress(data.roomState);
      delete data.roomState;
    }
    if (data.roomSummary) {
      this.roomSummary = new RoomSummary(data.roomSummary);
      delete data.roomSummary;
    }
    if (data.generatingQuestions) {
      this.generatingQuestions = new GeneratingQuestions(
        data.generatingQuestions
      );
      delete data.generatingQuestions;
    }
    // console.log(data);
    Object.assign(this, data);
  }
}
export class RoundData extends Schema {
  @type([Category]) categories = new ArraySchema<Category>();
  constructor(data: Partial<RoundData> = {}) {
    super();
    if (data.categories) {
      this.categories = new ArraySchema<Category>();
      data.categories.forEach(c => this.categories.push(new Category(c)));
      delete data.categories;
    }
    Object.assign(this, data);
  }
}
export class RoomData extends Schema {
  @type([RoundData]) rounds = new ArraySchema<RoundData>();
  constructor(data: Partial<RoomData> = {}) {
    super();
    if (data.rounds) {
      this.rounds = new ArraySchema<RoundData>();
      data.rounds.forEach(r => this.rounds.push(new RoundData(r)));
      delete data.rounds;
    }
    Object.assign(this, data);
  }
}

// --- ROOM ---
export class Room extends Schema {
  @type('string') roomId = '';
  @type('string') host = '';
  @type('number') mode = GameMode.TURN_BASED;
  @type('number') createdAt = 0;
  @type(['string']) playerIds = new ArraySchema<string>();
  @type([PlayerInfo]) playerInfos = new ArraySchema<PlayerInfo>();
  @type(['string']) categoryTitles = new ArraySchema<string>();
  @type(FullRoomState) gameState = new FullRoomState();
  @type(RoomSettings) roomSettings = new RoomSettings();
  @type(RoomData) roomData = new RoomData();

  constructor(data: Partial<Room> = {}) {
    super();
    if (data.gameState) {
      this.gameState = new FullRoomState(data.gameState);
      delete data.gameState;
    }
    if (data.roomSettings) {
      this.roomSettings = new RoomSettings(data.roomSettings);
      delete data.roomSettings;
    }
    if (data.roomData) {
      this.roomData = new RoomData(data.roomData);
      delete data.roomData;
    }
    if (data.playerInfos) {
      this.playerInfos = new ArraySchema<PlayerInfo>();
      data.playerInfos.forEach(pd => this.playerInfos.push(new PlayerInfo(pd)));
      delete data.playerInfos;
    }
    Object.assign(this, data);
  }
}

// --- NOTE ---
// This schema now strictly matches all fields in your Room interface and referenced types.
// All nested/optional/union fields are represented using Colyseus Schema subclasses and discriminators.
// You may need to further extend LogEntry, MemorableQuestion, etc. for full fidelity with your backend.

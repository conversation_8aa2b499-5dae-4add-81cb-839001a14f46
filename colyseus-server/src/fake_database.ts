import { IDatabase } from './database';
import { JeopardyRoomState } from './rooms/schema/JeopardyRoomState';

/**
 * A fake, in-memory database implementation for testing purposes.
 * It simulates the behavior of a real database without any external dependencies.
 */
export class FakeDatabase implements IDatabase {
  private rooms: Map<string, any> = new Map();

  async saveRoom(state: JeopardyRoomState): Promise<void> {
    console.log(`[FakeDatabase] Saving state for room ${state.roomId}`);
    // Store a plain JSON object to accurately simulate Firestore's behavior
    this.rooms.set(state.roomId, state.toJSON());
  }

  async loadRoom(roomId: string): Promise<JeopardyRoomState | null> {
    console.log(`[FakeDatabase] Loading state for room ${roomId}`);
    const roomData = this.rooms.get(roomId);
    if (roomData) {
      // Reconstruct the state from the plain object, just like FirestoreDatabase
      const roomState = new JeopardyRoomState(roomData);
      return roomState;
    }
    return null;
  }
}

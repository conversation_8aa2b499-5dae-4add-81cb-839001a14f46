import { DecodedIdToken, getAuth } from "firebase-admin/auth";

export interface IAuth {
    verifyIdToken(token: string): Promise<DecodedIdToken>;
    getUser(uid: string): Promise<any>;
}

export class FirebaseAdminAuthService implements IAuth {
    verifyIdToken(token: string): Promise<DecodedIdToken> {
        return getAuth().verifyIdToken(token);
    }

    getUser(uid: string): Promise<any> {
        return getAuth().getUser(uid);
    }
}

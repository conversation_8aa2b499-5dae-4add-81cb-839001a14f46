import { ArraySchema, Encoder } from '@colyseus/schema';
Encoder.BUFFER_SIZE = 40 * 1024; // 40 KB
import { Room, Client } from '@colyseus/core';
import {
  Room as JeopardyRoomState,
  GameMode,
  PlayerInfo,
  WaitingRoom,
  RoomStateProgress,
  RoomData,
  RoundData
} from '../room_schema';
import { LlmService } from '../llm_service';
import { updateSettingsHandler } from '../handlers/update_settings_handler';
import { startGenerationHandler } from '../handlers/start_generation_handler';
import { selectClueHandler } from '../handlers/select_clue_handler';
import { gameMasterActionHandler } from '../handlers/gamemaster_action_handler';
import { assignPointsHandler } from '../handlers/assign_points_handler';
import { endGameHandler } from '../handlers/end_game_handler';
import { regenerateClueHandler } from '../handlers/regenerate_clue_handler';
import { buzzInHandler } from '../handlers/buzz_in_handler';
import { submitGuess<PERSON><PERSON><PERSON> } from '../handlers/submit_guess_handler';
import { passBuzzHandler } from '../handlers/pass_buzz_handler';
import { skipShowAnswerHandler } from '../handlers/skip_show_answer_handler';
import { IDatabase } from '../database';
import { IAuth } from '../auth';
import { DecodedIdToken } from 'firebase-admin/auth';
import { endClue } from '../handlers/clue_lifecycle_helpers';
import { ServiceRegistry } from '../services';
import { waitingRoomActionHandler } from '../handlers/waiting_room_action_handler';
import { updateQuestionHandler } from '../handlers/update_question_handler';
import { castMediaCommandHandler } from '../handlers/cast_media_command_handler';
import { returnToBoardHandler } from '../handlers/return_to_board_handler';
import { resetQuestionHandler } from '../handlers/reset_question_handler';
import { reorderQuestionsHandler } from '../handlers/reorder_questions_handler';
import { sendChatMessage } from '../handlers/send_chat_message_handler';
import { generateUsername } from '../username_generator';

export class JeopardyRoom extends Room<JeopardyRoomState> {
  maxClients = 10;
  public activeAnswerTimer: any;
  public answerTimers: Map<string, any>;

  sessionIdToUidMap = new Map<string, string>();

  async onCreate(options: any) {
    console.log(`Creating new JeopardyRoom with options:`, options);

    this.setSeatReservationTime(15);
    if (options.roomId) {
      this.roomId = options.roomId;
    }
    this.registerMessageHandlers();
    if (options.roomDataFromDB) {
      this.state = options.roomDataFromDB;
      return;
    }
    this.activeAnswerTimer = null;
    this.answerTimers = new Map<string, any>();
    const initialState = new JeopardyRoomState();
    if (options.mode) {
      if (typeof options.mode === 'string') {
        switch (options.mode) {
          case 'multiplayer':
            initialState.mode = GameMode.TURN_BASED;
            initialState.roomSettings.gameMode = GameMode.TURN_BASED;
            break;
          case 'gamemaster':
            initialState.mode = GameMode.GAMEMASTER;
            initialState.roomSettings.gameMode = GameMode.GAMEMASTER;
            break;
          default:
            initialState.mode = GameMode.TURN_BASED;
            initialState.roomSettings.gameMode = GameMode.TURN_BASED;
            break;
        }
      } else {
        initialState.mode = options.mode;
        initialState.roomSettings.gameMode = options.mode;
      }
    }
    initialState.roomId = this.roomId;
    this.setState(initialState);
    ServiceRegistry.getInstance().database.saveRoom(this.state);
    console.log(
      `New room created with ID: ${this.roomId} and mode: ${
        GameMode[initialState.mode]
      }.`
    );
  }

  private registerMessageHandlers() {
    console.log('Registering message handlers...');
    this.onMessage('updateRoomSettings', updateSettingsHandler.bind(this));
    this.onMessage('startRoom', startGenerationHandler.bind(this));
    this.onMessage('selectClue', selectClueHandler.bind(this));
    this.onMessage('gameMasterActions', gameMasterActionHandler.bind(this));
    this.onMessage('assignPoints', assignPointsHandler.bind(this));
    this.onMessage('endGame', endGameHandler.bind(this));
    this.onMessage('regenerateClue', regenerateClueHandler.bind(this));
    this.onMessage(
      'regenerateClueWithInstructions',
      regenerateClueHandler.bind(this)
    );
    this.onMessage('buzzIn', buzzInHandler.bind(this));
    this.onMessage('submitGuess', submitGuessHandler.bind(this));
    this.onMessage('passBuzz', passBuzzHandler.bind(this));
    this.onMessage('skipShowAnswer', skipShowAnswerHandler.bind(this));
    this.onMessage('updateQuestion', updateQuestionHandler.bind(this));
    this.onMessage('resetQuestion', resetQuestionHandler.bind(this));
    this.onMessage(
      'reorderQuestionsWithAnswerStates',
      reorderQuestionsHandler.bind(this)
    );
    this.onMessage('castMediaCommand', castMediaCommandHandler.bind(this));
    this.onMessage('waitingRoomAction', waitingRoomActionHandler.bind(this));
    this.onMessage('returnToBoard', returnToBoardHandler.bind(this));
    this.onMessage('sendChatMessage', sendChatMessage.bind(this));
    console.log('Message handlers registered.');
  }

  async onAuth(client: Client, options: any) {
    if (options.isSpectator) {
      console.log(
        `Allowing spectator client ${client.sessionId} to join without authentication.`
      );
      return true; // Allow spectator to join without a valid token
    }
    console.log(`Authenticating client ${client.sessionId}...`);
    const user = await ServiceRegistry.getInstance().auth.verifyIdToken(
      options.token
    );
    console.log(
      `Client ${client.sessionId} authenticated successfully as user ${user.uid}.`
    );
    return user;
  }

  async onJoin(client: Client, options: any, auth: any) {
    console.log(
      `Client ${client.sessionId} is attempting to join room ${this.roomId}.`
    );
    if (options.isSpectator) {
      console.log(`Client ${client.sessionId} joined as a spectator.`);
      this.broadcast('messages', `A spectator is watching the game.`);
      return;
    }

    const userRecord = await ServiceRegistry.getInstance().auth.getUser(
      auth.uid
    );
    this.sessionIdToUidMap.set(client.sessionId, auth.uid);

    // Handle Gamemaster Mode logic first
    if (this.state.mode === GameMode.GAMEMASTER) {
      if (this.state.host === '' || this.state.host === auth.uid) {
        if (this.state.host === '') {
          this.state.host = auth.uid;
          console.log(
            `User ${auth.uid} joined as the host in Gamemaster mode.`
          );
          this.broadcast(
            'messages',
            `${userRecord.displayName} started the game as the Gamemaster.`
          );
        } else {
          console.log(`Host ${auth.uid} is rejoining the game.`);
          this.broadcast('messages', `${userRecord.displayName} rejoined.`);
        }
        return; // Do not proceed to add host to playerInfos
      } else {
        console.warn(
          `Rejecting player ${auth.uid} from joining Gamemaster game owned by ${this.state.host}.`
        );
        throw new Error('Only the host can join a game in Gamemaster mode.');
      }
    }

    // Standard join logic for other modes
    const existingPlayer = this.state.playerInfos.find(p => p.id === auth.uid);

    if (existingPlayer) {
      console.log(`User ${auth.uid} is rejoining the game.`);
      this.broadcast(
        'messages',
        `${existingPlayer.userName} rejoined the game.`
      );
      return;
    }

    if (this.state.gameState.progressType !== 'WaitingRoom') {
      console.warn(
        `Join failed: New user ${auth.uid} attempted to join a game already in progress.`
      );
      throw new Error('Game has already started and new players cannot join.');
    }

    console.log(`User ${auth.uid} is joining for the first time.`);
    const player = new PlayerInfo();
    player.id = auth.uid;
    player.userName = options.userName || generateUsername();
    player.email = userRecord.email;
    player.displayName = userRecord.displayName;
    player.photoUrl =
      userRecord.photoURL ||
      `https://getstream.io/random_png/?name=${player.userName}`;
    player.score = 0;

    this.state.playerInfos.push(player);
    this.state.playerIds.push(auth.uid);
    console.log(`Player ${player.userName} (${auth.uid}) added to the room.`);

    if (this.state.host === '') {
      this.state.host = auth.uid;
      console.log(
        `User ${auth.uid} is the first to join and has been set as the host.`
      );
    }
    ServiceRegistry.getInstance().database.saveRoom(this.state);

    this.broadcast('messages', `${player.userName} joined the game.`);
    console.log(`Broadcasted join message for ${player.userName}.`);
  }

  onLeave(client: Client, consented: boolean) {
    const uid = this.sessionIdToUidMap.get(client.sessionId);
    console.log(
      `Client ${client.sessionId} (user ${uid}) left the room. Consented: ${consented}.`
    );

    if (!uid) return;

    const player = this.state.playerInfos.find(p => p.id === uid);
    if (player) {
      console.log(
        `Player ${player.userName} (${uid}) left the room. Their data is preserved for reconnection.`
      );
      this.broadcast('messages', `${player.userName} left the game.`);
    }
    if (consented) {
      this.sessionIdToUidMap.delete(client.sessionId);
      return;
    }
    this.allowReconnection(client, 5);
    this.sessionIdToUidMap.delete(client.sessionId);
  }

  onDispose() {
    console.log(`Room ${this.roomId} is being disposed.`);
  }

  /**
   * Restores the room's state from persistent data.
   * This method is called by your custom getOrCreate endpoint.
   * @param stateDataFromDB - The data retrieved from your database.
   */
  async restoreState(stateDataFromDB: any) {
    this.roomId = stateDataFromDB.roomId;
    console.log(`Restoring state for room ${this.roomId}...`);

    // Create a new state instance from the database data.
    // Your `JeopardyRoomState` constructor might need to handle this.
    const newState = new JeopardyRoomState(stateDataFromDB);
    this.setState(newState);

    // You can also restore other room properties if needed.
    // For example:
    // if (stateDataFromDB.isLocked) { this.lock(); }
    // if (stateDataFromDB.customData) { this.customData = stateDataFromDB.customData; }
  }
}

import { Schema, type, ArraySchema, entity } from '@colyseus/schema';

export enum LogType {
  CLUE_SELECTED = 'CLUE_SELECTED',
  BUZZ_IN = 'BUZZ_IN',
  ANSWER_CORRECT = 'ANSWER_CORRECT',
  ANSWER_INCORRECT = 'ANSWER_INCORRECT',
  PASS = 'PASS',
  TIMER_EXPIRED = 'TIMER_EXPIRED',
  HINT_OPENED = 'HINT_OPENED',
  NO_CORRECT_ANSWER = 'NO_CORRECT_ANSWER',
  WAITING_FOR_ANSWER = 'WAITING_FOR_ANSWER',
  SHOW_ANSWER = 'SHOW_ANSWER',
  POINTS_ASSIGNED = 'POINTS_ASSIGNED',
  UNKNOWN = 'UNKNOWN'
}

export class BaseLogEntry extends Schema {
  @type('string') type: LogType = LogType.UNKNOWN;
  @type('number') timestamp: number = 0;
  @type('string') snarkyComment?: string = '<SNARKY_COMMENT>';
}

export class ClueSelectedLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('string') categoryTitle: string = '';
  @type('number') clueValue: number = 0;
}

export class BuzzInLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('number') timeTaken: number = 0;
}

export class AnswerCorrectLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('string') guess: string = '';
  @type('number') pointsAwarded: number = 0;
}

export class AnswerIncorrectLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('string') guess: string = '';
  @type('number') pointsDeducted: number = 0;
}

export class PassLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('number') pointsDeducted?: number = 0;
}

export class TimerExpiredLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
  @type('number') pointsDeducted?: number = 0;
}

@entity
export class HintOpenedLog extends BaseLogEntry {}

@entity
export class NoCorrectAnswerLog extends BaseLogEntry {}

export class WaitingForAnswerLog extends BaseLogEntry {
  @type('string') playerId: string = '';
  @type('string') playerName: string = '';
}

export class ShowAnswerLog extends BaseLogEntry {
  @type('string') answer: string = '';
}

export class PointsAssignedLog extends BaseLogEntry {
  @type('string') playerId?: string = '';
  @type('string') playerName?: string = '';
  @type('string') teamName?: string = '';
  @type('number') pointsAwarded: number = 0;
}

// export type LogEntry =
//   | ClueSelectedLog
//   | BuzzInLog
//   | AnswerCorrectLog
//   | AnswerIncorrectLog
//   | PassLog
//   | TimerExpiredLog
//   | HintOpenedLog
//   | NoCorrectAnswerLog
//   | WaitingForAnswerLog
//   | ShowAnswerLog
//   | PointsAssignedLog;

// 1. Create the Registry (The Factory)
const logRegistry = new Map<LogType, new (...args: any[]) => BaseLogEntry>([
  [LogType.CLUE_SELECTED, ClueSelectedLog],
  [LogType.BUZZ_IN, BuzzInLog],
  [LogType.ANSWER_CORRECT, AnswerCorrectLog],
  [LogType.ANSWER_INCORRECT, AnswerIncorrectLog],
  [LogType.PASS, PassLog],
  [LogType.TIMER_EXPIRED, TimerExpiredLog],
  [LogType.HINT_OPENED, HintOpenedLog],
  [LogType.NO_CORRECT_ANSWER, NoCorrectAnswerLog],
  [LogType.WAITING_FOR_ANSWER, WaitingForAnswerLog],
  [LogType.SHOW_ANSWER, ShowAnswerLog],
  [LogType.POINTS_ASSIGNED, PointsAssignedLog]
]);

export class StructuredClueLog extends Schema {
  @type({ array: BaseLogEntry }) entries = new ArraySchema<BaseLogEntry>();

  constructor(data: Partial<StructuredClueLog> = {}) {
    super();
    if (data.entries) {
      this.entries = new ArraySchema<BaseLogEntry>();
      data.entries.forEach(e => {
        // Find the correct class in the registry
        const LogClass = logRegistry.get(e.type);
        if (LogClass) {
          const logEntry = new LogClass(e);
          logEntry.assign(e);
          data.entries!.push(logEntry);
        }
      });
      delete data.entries;
    }
  }
}

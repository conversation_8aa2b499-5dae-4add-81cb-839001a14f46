// src/services.ts
import { IDatabase } from './database';
import { IAuth } from './auth';
import { LlmService } from './llm_service';
import { FirestoreDatabase } from './firestore_database';
import { FirebaseAdminAuthService } from './auth';
import { LlmApiService } from './llm_service';

/**
 * A singleton registry for global services.
 * This allows for a single point of access for services like database, auth, etc.,
 * and makes it easy to swap them out for mocks in tests.
 */
export class ServiceRegistry {
  private static instance: ServiceRegistry;

  public readonly database: IDatabase;
  public readonly auth: IAuth;
  public readonly llm: LlmService;

  private constructor(database: IDatabase, auth: IAuth, llm: LlmService) {
    this.database = database;
    this.auth = auth;
    this.llm = llm;
  }

  /**
   * Initializes the service registry with either production services or provided mocks.
   * This should be called once at application startup.
   */
  public static initialize(services?: {
    database?: IDatabase;
    auth?: IAuth;
    llm?: LlmService;
  }): void {
    if (this.instance) {
      console.warn('ServiceRegistry has already been initialized.');
      return;
    }

    const database = services?.database; // || new FirestoreDatabase();
    const auth = services?.auth; // || new FirebaseAdminAuthService();
    const llm = services?.llm; // || new LlmApiService();

    this.instance = new ServiceRegistry(database, auth, llm);
    console.log('ServiceRegistry initialized.');
  }

  public static destroy() {
    this.instance = undefined;
  }

  /**
   * Returns the singleton instance of the ServiceRegistry.
   * Throws an error if the registry has not been initialized.
   */
  public static getInstance(): ServiceRegistry {
    if (!this.instance) {
      throw new Error(
        'ServiceRegistry has not been initialized. Call initialize() first.'
      );
    }
    return this.instance;
  }
}

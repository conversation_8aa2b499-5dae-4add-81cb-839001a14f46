import { LlmAgent } from './room_schema';
import { GoogleGenerativeAI } from '@google/generative-ai';
import OpenAI from 'openai';
import fs from 'fs';

/**
 * Defines the interface for a service that can interact with a Large Language Model.
 */
export class LlmService {
  async generate(prompt: string, llmAgent: LlmAgent): Promise<string> {
    throw new Error('Not implemented');
  }
  async *generateStream(
    prompt: string,
    llmAgent: LlmAgent
  ): AsyncGenerator<string, void, unknown> {
    yield await this.generate(prompt, llmAgent);
  }
}

/**
 * A concrete implementation of LlmService that uses the real LLM APIs.
 */
export class LlmApiService extends LlmService {
  async generate(prompt: string, llmAgent: LlmAgent): Promise<string> {
    // This can be a simple, non-streaming wrapper if needed,
    // or be deprecated in favor of generateStream.
    let fullResponse = '';
    for await (const chunk of this.generateStream(prompt, llmAgent)) {
      fullResponse += chunk;
    }
    return fullResponse;
  }

  async *generateStream(
    prompt: string,
    llmAgent: LlmAgent
  ): AsyncGenerator<string, void, unknown> {
    switch (llmAgent.provider) {
      case 'Gemini':
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
        const model = genAI.getGenerativeModel({ model: llmAgent.modelName });
        const result = await model.generateContentStream(prompt);
        for await (const chunk of result.stream) {
          yield chunk.text();
        }
        break;

      case 'OpenAI':
        const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });
        const stream = await openai.chat.completions.create({
          messages: [{ role: 'system', content: prompt }],
          model: llmAgent.modelName,
          stream: true
        });
        for await (const chunk of stream) {
          yield chunk.choices[0]?.delta?.content || '';
        }
        break;

      case 'DeepSeekAI':
        const deepseek = new OpenAI({
          apiKey: process.env.DEEPSEEK_API_KEY!,
          baseURL: 'https://api.deepseek.com/v1'
        });
        const deepseekStream = await deepseek.chat.completions.create({
          messages: [{ role: 'system', content: prompt }],
          model: llmAgent.modelName,
          stream: true
        });
        for await (const chunk of deepseekStream) {
          yield chunk.choices[0]?.delta?.content || '';
        }
        break;

      case 'FakeReal':
        const fakeRealResponse = fs.readFileSync(
          'src/assets/fake_real_response.txt',
          'utf8'
        );
        for (let i = 0; i < fakeRealResponse.length; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 50));
          yield fakeRealResponse.substring(i, i + 10);
        }
        break;
      case 'Fake':
        const fakeResponse = JSON.stringify(
          [
            {
              categoryTitle: 'Fake Category',
              questions: [
                {
                  answer: 'Fake Answer',
                  value: 100,
                  questionHTML: 'Fake Question',
                  hint: 'Fake Hint',
                  answerExplanation: ['Fake Explanation']
                }
              ]
            }
          ],
          null,
          2
        );
        for (let i = 0; i < fakeResponse.length; i += 5) {
          await new Promise(resolve => setTimeout(resolve, 50));
          yield fakeResponse.substring(i, i + 5);
        }
        break;

      default:
        throw new Error(`Unsupported LLM provider: ${llmAgent.provider}`);
    }
  }
}

/**
 * A mock implementation of LlmService for testing purposes.
 * It returns a predefined, valid JSON response without making any real API calls.
 */
export class MockLlmService extends LlmService {
  override async generate(prompt: string, llmAgent: LlmAgent): Promise<string> {
    let fullResponse = '';
    for await (const chunk of this.generateStream(prompt, llmAgent)) {
      fullResponse += chunk;
    }
    return fullResponse;
  }
  async *generateStream(
    prompt: string,
    llmAgent: LlmAgent
  ): AsyncGenerator<string, void, unknown> {
    console.log(
      `MockLlmService: Simulating LLM stream for ${llmAgent.provider} - ${llmAgent.modelName}`
    );
    const fakeApiResponse =
      '```json\n' +
      JSON.stringify(
        [
          {
            broadArea: 'Mock',
            categoryTitle: 'Mock Category',
            categoryDescription:
              'This is a mock category generated for testing.',
            questions: [
              {
                answer: 'Mock Answer',
                value: 100,
                questionHTML: '<div>This is a mock question?</div>',
                answerExplanation: ["Because it's a mock."],
                hint: 'The hint is... mock.'
              }
            ]
          }
        ],
        null,
        2
      ) +
      '\n```';

    for (let i = 0; i < fakeApiResponse.length; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 10)); // Simulate network delay
      yield fakeApiResponse.substring(i, i + 10);
    }
  }
}

import { Server } from 'colyseus';
import { createServer as createHttpServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { readFileSync } from 'fs';
import express from 'express';
import * as admin from 'firebase-admin';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

// Import Colyseus config
import appConfig from './app.config';
import { ServiceRegistry } from './services';
import { FirestoreDatabase } from './firestore_database';
import { FirebaseAdminAuthService } from './auth';
import { LlmApiService } from './llm_service';

console.log('Process.env: ', process.env);
// This function fetches the key from Secret Manager
async function getFirebaseCredentials() {
  const client = new SecretManagerServiceClient();
  const secretName =
    'projects/jeopardy-gpt/secrets/firebase-service-account-key/versions/latest';
  const [version] = await client.accessSecretVersion({ name: secretName });
  const payload = version.payload?.data?.toString();

  if (!payload) {
    throw new Error(
      'Could not fetch Firebase credentials from Secret Manager.'
    );
  }
  return JSON.parse(payload);
}

async function initializeFirebase() {
  console.log('Initializing Firebase Admin SDK...');
  const credential = admin.credential.cert(await getFirebaseCredentials());
  admin.initializeApp({ credential });

  console.log('Firebase Admin SDK successfully initialized.');
}

async function main() {
  if (process.env.PRODUCTION == 'true') {
    await initializeFirebase();
  } else {
    admin.initializeApp();
  }
  ServiceRegistry.initialize({
    database: new FirestoreDatabase(),
    auth: new FirebaseAdminAuthService(),
    llm: new LlmApiService()
  });

  const port = Number(process.env.PORT || 2567);
  const expressApp = express();

  // Initialize the express app with the routes and middleware from app.config.ts
  if (appConfig.initializeExpress) {
    appConfig.initializeExpress(expressApp);
  }

  const gameServer = new Server();

  // Initialize the game server with the room handlers from app.config.ts
  if (appConfig.initializeGameServer) {
    appConfig.initializeGameServer(gameServer);
  }

  // Logic to handle HTTPS in production
  if (process.env.PRODUCTION === 'true') {
    const domain = 'jeopardygpt-colyseus.pulkitg10.com';
    let credentials;

    // --- Certificate Loading Logic ---
    // 1. Try to load the real Let's Encrypt certificate
    try {
      const certPath = `/etc/letsencrypt/live/${domain}/`;
      credentials = {
        key: readFileSync(`${certPath}privkey.pem`, 'utf8'),
        cert: readFileSync(`${certPath}fullchain.pem`, 'utf8')
      };
      console.log("Successfully loaded Let's Encrypt certificate.");
    } catch (e) {
      console.warn(
        "Let's Encrypt certificate not found:",
        (e as Error).message
      );
      // 2. If that fails, try to load the fallback self-signed certificate
      try {
        credentials = {
          key: readFileSync('/etc/ssl/self-signed/key.pem', 'utf8'),
          cert: readFileSync('/etc/ssl/self-signed/cert.pem', 'utf8')
        };
        console.warn(
          'Using fallback self-signed certificate. Browser will show a warning.'
        );
      } catch (e2) {
        console.error(
          'Self-signed certificate not found either:',
          (e2 as Error).message
        );
        console.error(
          'CRITICAL: Could not start HTTPS server. Falling back to HTTP.'
        );
        credentials = null; // Explicitly set to null
      }
    }

    // --- Server Creation Logic ---
    if (credentials) {
      const httpsServer = createHttpsServer(credentials, expressApp);
      gameServer.attach({ server: httpsServer });

      httpsServer.listen(443, () => {
        console.log(`🚀 Secure server is listening on port 443`);
      });

      // Redirect HTTP to HTTPS
      createHttpServer((req, res) => {
        res.writeHead(301, {
          Location: `https://${req.headers.host}${req.url}`
        });
        res.end();
      }).listen(80);

      console.log('Production server setup with HTTPS.');
    } else {
      // Fallback to HTTP if no certificates could be loaded
      const httpServer = createHttpServer(expressApp);
      gameServer.attach({ server: httpServer });
      httpServer.listen(port, () => {
        console.log(`🚀 Insecure server listening on http://localhost:${port}`);
      });
    }
  } else {
    // Local development server
    try {
      // Try to create an HTTPS server for local testing
      const privateKey = readFileSync(`key.pem`, 'utf8');
      const certificate = readFileSync(`cert.pem`, 'utf8');
      const credentials = { key: privateKey, cert: certificate };

      const httpsPort = 2568; // Use a different port for local HTTPS
      const httpsServer = createHttpsServer(credentials, expressApp);
      gameServer.attach({ server: httpsServer });

      httpsServer.listen(httpsPort, () => {
        console.log(
          `🚀 Secure development server is listening on https://localhost:${httpsPort}`
        );
      });
    } catch (e) {
      // Fallback to HTTP if local certs are not found
      console.log('Local SSL certificates not found, falling back to HTTP.');
      const httpServer = createHttpServer(expressApp);
      gameServer.attach({ server: httpServer });
      httpServer.listen(port, () => {
        console.log(
          `🚀 Development server is listening on http://localhost:${port}`
        );
      });
    }
  }

  if (appConfig.beforeListen) {
    appConfig.beforeListen();
  }
}

// --- Run the main function and catch any errors ---
main().catch(err => {
  console.error('Failed to start server:', err);
  process.exit(1);
});

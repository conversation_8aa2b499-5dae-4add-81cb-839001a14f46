
import { IDatabase } from "./database";
import { JeopardyRoomState } from "./rooms/schema/JeopardyRoomState";

export class MockDatabase implements IDatabase {
  private rooms: Map<string, JeopardyRoomState> = new Map();

  async saveRoom(state: JeopardyRoomState): Promise<void> {
    this.rooms.set(state.roomId, state);
    return Promise.resolve();
  }

  async loadRoom(roomId: string): Promise<JeopardyRoomState | null> {
    const room = this.rooms.get(roomId);
    return Promise.resolve(room || null);
  }
}

import { IDatabase } from './database';
import { JeopardyRoomState } from './rooms/schema/JeopardyRoomState';
import { Room } from 'colyseus';
import * as admin from 'firebase-admin';

export class FirestoreDatabase implements IDatabase {
  db: FirebaseFirestore.Firestore;

  constructor() {
    console.log('FirestoreDatabase initialized.');
    this.db = admin.firestore();
  }
  async saveRoom(state: JeopardyRoomState): Promise<void> {
    const plainState: JeopardyRoomState = JSON.parse(
      JSON.stringify(state, null, 2)
    );
    await this.db
      .collection('rooms')
      .doc(state.roomId)
      .set(plainState);
    console.log(`[Firestore] Saved state for room ${state.roomId}`);
  }

  async loadRoom(roomId: string): Promise<JeopardyRoomState | null> {
    const doc = await this.db
      .collection('rooms')
      .doc(roomId)
      .get();
    if (!doc.exists) {
      return null;
    }
    console.log(`[Firestore] Loaded state for room ${roomId}`);
    const roomState = new JeopardyRoomState(doc.data() as JeopardyRoomState);
    return roomState;
  }
}

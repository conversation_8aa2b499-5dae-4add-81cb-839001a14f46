{"private": true, "name": "my-app", "version": "1.0.0", "description": "npm init template for bootstrapping an empty Colyseus project", "main": "build/index.js", "engines": {"node": ">= 20.9.0"}, "scripts": {"start": "tsx watch src/index.ts", "loadtest": "tsx loadtest/example.ts --room my_room --numClients 2", "build:tsc": "tsc", "build:copy-assets": "cpx \"src/assets/**/*.txt\" build/assets/", "build": "npm run build:tsc && npm run build:copy-assets", "clean": "<PERSON><PERSON><PERSON> build", "test": "mocha \"test/**/*.ts\"", "test:file": "mocha"}, "author": "", "license": "UNLICENSED", "bugs": {"url": "https://github.com/colyseus/create-colyseus/issues"}, "homepage": "https://github.com/colyseus/create-colyseus#readme", "devDependencies": {"@colyseus/loadtest": "^0.16.0", "@colyseus/testing": "^0.16.3", "@types/cors": "^2.8.19", "@types/express": "^4.17.1", "@types/js-levenshtein": "^1.1.3", "@types/mocha": "^10.0.1", "@types/sinon": "^17.0.4", "cpx": "^1.5.0", "mocha": "^10.2.0", "rimraf": "^5.0.0", "sinon": "^21.0.0", "tsx": "^4.10.2", "typescript": "^5.0.4"}, "dependencies": {"@colyseus/monitor": "^0.16.0", "@colyseus/playground": "^0.16.0", "@colyseus/tools": "^0.16.0", "@google-cloud/secret-manager": "^6.1.0", "@google/generative-ai": "^0.24.1", "@types/chai": "^5.2.2", "chai": "^5.2.0", "colyseus": "^0.16.0", "cors": "^2.8.5", "express": "^4.18.2", "firebase-admin": "^13.4.0", "js-levenshtein": "^1.1.6", "node-fetch": "^3.3.2", "openai": "^5.8.2"}}
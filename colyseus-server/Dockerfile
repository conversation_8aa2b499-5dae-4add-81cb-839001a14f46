# Dockerfile

### STAGE 1: Build ###
# This stage compiles your TypeScript into JavaScript
FROM node:24-alpine3.22 AS builder
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

### STAGE 2: Production ###
# This stage creates the final, lean production image
FROM node:24-alpine3.22
WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/package*.json ./

# Install tools, install packages, remove tools, AND clear the npm cache all in one layer.
RUN apk add --no-cache --virtual .build-deps python3 make g++ && \
    npm install --omit=dev && \
    apk del .build-deps && \
    npm cache clean --force

# Clean up unused uWebSockets.js binaries
RUN ARCH=$(apk --print-arch | sed 's/x86_64/x64/') && \
    find /usr/src/app/node_modules/uWebSockets.js -name "*.node" -not -name "uws_linux_${ARCH}_*.node" -delete

# Copy the compiled application code
COPY --from=builder /usr/src/app/build ./build

# Expose the correct ports for HTTP and HTTPS
EXPOSE 80 443

# Set the environment to production
ENV NODE_ENV=production

# The command to run your compiled application
CMD [ "node", "build/index.js" ]
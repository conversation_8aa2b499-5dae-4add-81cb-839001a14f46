#!/bin/bash
#
# Smart startup script for Colyseus on a GCE VM.
# This script is run as root by the GCE startup agent.
# It is fully non-interactive.
#
# - Installs Docker.
# - Installs Certbot and gets an SSL certificate if one doesn't exist.
# - Deploys the Colyseus server in a Docker container with HTTPS enabled.
#

# --- Configuration ---
# IMPORTANT: Replace with your domain and the email for Let's Encrypt renewal notices.
DOMAIN="jeopardygpt-colyseus.pulkitg10.com"
EMAIL="<EMAIL>" # Replace this!

# --- 1. Install Docker ---
echo "--- Installing Docker ---"
apt-get update
apt-get install -y apt-transport-https ca-certificates curl gnupg2 software-properties-common
curl -fsSL https://download.docker.com/linux/debian/gpg | apt-key add -
add-apt-repository -y "deb [arch=amd64] https://download.docker.com/linux/debian $(lsb_release -cs) stable"
apt-get update
apt-get install -y docker-ce

# --- 2. Install Certbot and Get Certificate (if needed) ---
echo "--- Checking for SSL Certificate ---"
if [ ! -d "/etc/letsencrypt/live/$DOMAIN" ]; then
  echo "Certificate not found for $DOMAIN. Installing Certbot and obtaining one..."
  
  # Stop any service that might be using port 80
  systemctl stop apache2 || true
  systemctl stop nginx || true
  
  # Install Certbot using snap
  apt-get install -y snapd
  snap install --classic certbot
  ln -s /snap/bin/certbot /usr/bin/certbot
  
  # Obtain the certificate non-interactively
  if certbot certonly --standalone --non-interactive --agree-tos -m "$EMAIL" -d "$DOMAIN"; then
    echo "Certificate obtained successfully from Let's Encrypt."
  else
    echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    echo "!!! Certbot failed. Falling back to a self-signed certificate. !!!"
    echo "!!! The server will be accessible, but will show a browser warning. !!!"
    echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    # Check if a self-signed certificate already exists before creating a new one
    if [ ! -f "/etc/ssl/self-signed/cert.pem" ]; then
      echo "Self-signed certificate not found. Generating a new one..."
      mkdir -p /etc/ssl/self-signed
      openssl req -x509 -newkey rsa:2048 -keyout /etc/ssl/self-signed/key.pem -out /etc/ssl/self-signed/cert.pem -sha256 -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/OU=OrgUnit/CN=$DOMAIN"
    else
      echo "Existing self-signed certificate found. Using it."
    fi
  fi
else
  echo "Certificate found for $DOMAIN. Skipping installation."
fi

# --- 3. Configure Docker and GCP ---
echo "--- Configuring GCP and Docker Auth ---"
gcloud auth configure-docker us-central1-docker.pkg.dev --quiet

# Pull the latest Docker image
echo "--- Pulling latest Docker image ---"
docker pull us-central1-docker.pkg.dev/jeopardy-gpt/colyseus-repo/colyseus-server:latest

# --- 4. Configure Logging Agent ---
echo "--- Configuring Ops Agent for Docker ---"
tee /etc/google-cloud-ops-agent/config.yaml > /dev/null <<'EOF'
logging:
  receivers:
    docker_logs:
      type: files
      include_paths:
        - /var/lib/docker/containers/*/*.log
  processors:
    docker_logs:
      type: parse_regex
      regex: '^{"log":"(?<message>.*)","stream":".*","time":".*"}'
  service:
    pipelines:
      default_pipeline:
        receivers: [docker_logs]
        processors: [docker_logs]
EOF

# Install/Update the Ops Agent if it's not already installed
if ! dpkg -s google-cloud-ops-agent >/dev/null 2>&1; then
  echo "Ops Agent not found. Installing..."
  curl -sSO https://dl.google.com/cloudagents/add-google-cloud-ops-agent-repo.sh
  bash add-google-cloud-ops-agent-repo.sh --also-install
else
  echo "Ops Agent already installed. Skipping installation."
fi

# --- 5. Run the Docker Container ---
echo "--- Fetching environment variables and running container ---"
# Note: Ensure the user running the script has permission for gcloud secrets
gcloud secrets versions access latest --secret="colyseus-env" > /home/<USER>/env

# Stop and remove any existing container with the same name to avoid conflicts
docker stop colyseus-server || true
docker rm colyseus-server || true

# Run the Docker container with HTTPS configuration
docker run \
  --name colyseus-server \
  -d \
  -p 80:80 \
  -p 443:443 \
  --restart=always \
  --env-file=/home/<USER>/env \
  -v /etc/letsencrypt:/etc/letsencrypt:ro \
  -v /etc/ssl/self-signed:/etc/ssl/self-signed:ro \
  us-central1-docker.pkg.dev/jeopardy-gpt/colyseus-repo/colyseus-server:latest

echo "--- Startup script finished. Colyseus server should be running. ---"

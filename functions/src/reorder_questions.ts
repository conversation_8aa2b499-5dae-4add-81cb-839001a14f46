import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Round, Clue} from "./resources";

interface ReorderQuestionsRequest {
  roomId: string;
  roundIdx: number;
  reorderedCategories: {
    categoryIdx: number;
    clues: Clue[];
  }[];
}

export const reorderQuestions = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as ReorderQuestionsRequest;

    if (!req.roomId) {
      throw new HttpsError("invalid-argument", "Room ID is required");
    }

    if (req.roundIdx === undefined || req.roundIdx < 0) {
      throw new HttpsError("invalid-argument", "Valid round index is required");
    }

    if (!req.reorderedCategories || !Array.isArray(req.reorderedCategories)) {
      throw new HttpsError(
        "invalid-argument",
        "Reordered categories data is required"
      );
    }

    try {
      const db = admin.firestore();

      return await db.runTransaction(async (transaction) => {
        const roundRef = db
          .collection("rooms")
          .doc(req.roomId)
          .collection("roomRoundData")
          .doc(req.roundIdx.toString());

        const roundDoc = await transaction.get(roundRef);

        if (!roundDoc.exists) {
          throw new HttpsError("not-found", "Round data not found");
        }

        const round = roundDoc.data() as Round;

        // Update the clues for each affected category
        req.reorderedCategories.forEach(({categoryIdx, clues}) => {
          if (categoryIdx >= 0 && categoryIdx < round.categories.length) {
            round.categories[categoryIdx].clues = clues;
          }
        });

        // Save the updated round data
        transaction.set(roundRef, round);

        return round;
      });
    } catch (error) {
      console.error("Error reordering questions:", error);
      if (error instanceof HttpsError) {
        throw error;
      }
      throw new HttpsError("internal", "Failed to reorder questions");
    }
  }
);

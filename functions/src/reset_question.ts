import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Room, RoomState, ClueState} from "./resources";

interface ResetQuestionRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export const resetQuestion = functions.https.onCall(
  async (data: any, _context: any) => {
    try {
      const req = data.data as ResetQuestionRequest;
      const db = admin.firestore();

      if (
        !req.roomId ||
        req.roundIdx == null ||
        req.categoryIdx == null ||
        req.clueIdx == null
      ) {
        throw new HttpsError("invalid-argument", "Missing required parameters");
      }

      return await db.runTransaction(async (transaction) => {
        const roomRef = db.collection("rooms").doc(req.roomId);
        const roomDoc = await transaction.get(roomRef);

        if (!roomDoc.exists) {
          throw new HttpsError("not-found", "Room not found");
        }

        const roomData = roomDoc.data() as Room;

        // Check if we're in an active game state
        const gameState = roomData.gameState?.gameProgress;
        if (gameState?.type !== "RoomState") {
          throw new HttpsError(
            "failed-precondition",
            "Game is not in active state"
          );
        }

        const roomState = gameState as RoomState;
        const roundState = roomState.roundStates[req.roundIdx];

        if (!roundState) {
          throw new HttpsError("invalid-argument", "Invalid round index");
        }

        if (req.categoryIdx >= roundState.categoryStates.length) {
          throw new HttpsError("invalid-argument", "Invalid category index");
        }

        const categoryState = roundState.categoryStates[req.categoryIdx];
        if (req.clueIdx >= categoryState.clueStates.length) {
          throw new HttpsError("invalid-argument", "Invalid clue index");
        }

        const clueState = categoryState.clueStates[req.clueIdx];

        // Check if question was actually answered
        if (!clueState.answeredByPlayerId && !clueState.clueComplete) {
          throw new HttpsError(
            "failed-precondition",
            "Question was not answered"
          );
        }

        // Get the round data to find the point value
        const roundRef = roomRef
          .collection("roomRoundData")
          .doc(req.roundIdx.toString());
        const roundDoc = await transaction.get(roundRef);

        if (!roundDoc.exists) {
          throw new HttpsError("not-found", "Round data not found");
        }

        const round = roundDoc.data() as any; // Round type
        const clue = round.categories[req.categoryIdx].clues[req.clueIdx];

        // Revert player score if question was answered
        if (clueState.answeredByPlayerId) {
          const playerIndex = roomData.playerIds.indexOf(
            clueState.answeredByPlayerId
          );
          if (playerIndex !== -1) {
            let pointsToRevert = clue.value;

            // If hint was opened, points were halved
            if (clueState.hintOpened) {
              pointsToRevert = Math.floor(clue.value / 2);
            }

            // Subtract the points that were previously awarded
            roomData.playerInfos[playerIndex].score -= pointsToRevert;
          }
        }

        // Reset the clue state to initial state
        const resetClueState: ClueState = {
          hintOpened: false,
          showAnswer: false,
          buzzedInPlayerQueue: [],
          passedPlayers: [],
          queueAnswerTurnIdx: -1,
          clueComplete: false,
          clueLog: {logs: []},
          answerExplanations: [],
        };

        // Update the clue state
        roundState.categoryStates[req.categoryIdx].clueStates[
          req.clueIdx
        ] = resetClueState;

        // Update room data with modified game state
        roomData.gameState.gameProgress = roomState;

        // Save the updated room data
        transaction.set(roomRef, roomData);

        return {
          success: true,
          message: "Question reset successfully",
        };
      });
    } catch (error) {
      console.error("Error resetting question:", error);
      if (error instanceof HttpsError) {
        throw error;
      }
      throw new HttpsError("internal", "Failed to reset question");
    }
  }
);

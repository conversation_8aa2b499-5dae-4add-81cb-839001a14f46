import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Round} from "./resources";

interface GetRoundDataRequest {
  roomId: string;
  roundIdx: number;
}

export const getRoundData = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as GetRoundDataRequest;

    if (!req.roomId) {
      throw new HttpsError("invalid-argument", "Room ID is required");
    }

    if (req.roundIdx === undefined || req.roundIdx < 0) {
      throw new HttpsError("invalid-argument", "Valid round index is required");
    }

    try {
      const db = admin.firestore();
      const roundRef = db
        .collection("rooms")
        .doc(req.roomId)
        .collection("roomRoundData")
        .doc(req.roundIdx.toString());

      const roundDoc = await roundRef.get();

      if (!roundDoc.exists) {
        throw new HttpsError("not-found", "Round data not found");
      }

      return roundDoc.data() as Round;
    } catch (error) {
      console.error("Error getting round data:", error);
      if (error instanceof HttpsError) {
        throw error;
      }
      throw new HttpsError("internal", "Failed to get round data");
    }
  }
);

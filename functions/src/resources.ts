import * as admin from 'firebase-admin';
export interface Clue {
  questionSentences: Array<string>;
  questionHTML: string;
  hint: string;
  hintBlanks: string;
  answer: string;
  value: number;
  detailedFactsAboutAnswer: Array<string>;
  isGoodClue?: boolean;
}

export interface Category {
  categoryTitle: string;
  categoryDescription?: string;
  clues: Array<Clue>;
}

export interface Round {
  categories: Array<Category>;
}

export interface Player {
  playerId: string;
  username: string;
  score: number;
}

export interface PlayerInfo {
  score: number;
  userName?: string;
  photoUrl?: string;
}

export enum GameMode {
  TURN_BASED = 1,
  BUZZER = 2,
  GAMEMASTER = 3
}

export interface LlmAgent {
  provider: 'OpenAI' | 'Gemini' | 'DeepSeekAI' | 'Fake' | 'FakeReal';
  modelName: string;
}

export interface RoomSettings {
  gameMode: GameMode;
  buzzInTimerDurationMillis: number;
  answerDurationMillis: number;
  showAnswerDurationMillis: number;
  initialThinkingDurationMillis: number;
  llmAgent: LlmAgent;
  numQuestions?: number;
  generationStrategy?: number;
}

export interface Room {
  roomId: string;
  host: string; // playerId of the host
  mode: GameMode;
  createdAt: number;
  // In non-gamemaster mode, playerIds are UIDs.
  // In gamemaster mode, playerIds are just indices.
  playerIds: Array<string>;
  playerInfos: Array<PlayerInfo>;
  categoryTitles?: Array<string>;

  gameState: FullRoomState;
  roomSettings?: RoomSettings;
  roomData?: {
    rounds: Round[];
  };
}

export interface ClueLog {
  logs: Array<string>;
}

export interface AnswerExplanation {
  isCorrect: boolean;
  explanation: string;
}

import { StructuredClueLog } from './structured_log';
export * from './structured_log';

export interface ClueState {
  hintOpened: boolean;
  showAnswer: boolean;
  buzzInTimerStartTime?: number;
  buzzedInPlayerQueue: Array<string>;
  passedPlayers: Array<string>;
  // Which idx in buzzedInPlayerQueue has to answer next.
  queueAnswerTurnIdx: number;
  answerStartTime?: number;
  showAnswerStartTime?: number;
  skipShowAnswer?: Array<string>;
  clueComplete?: boolean;
  clueLog?: ClueLog;
  structuredClueLog?: StructuredClueLog; // New field for structured logs
  answerExplanations: AnswerExplanation[];
  answeredByPlayerId?: string;
  // Countdown timer fields for gamemaster mode
  countdownStartTime?: number; // When the countdown timer started
  countdownDurationSeconds?: number; // Duration of the countdown in seconds
  countdownActive?: boolean; // Whether the countdown is currently active
}

export interface BuzzMessage {
  playerId: string;
  buzzTime: admin.firestore.Timestamp;
}

export interface CategoryState {
  clueStates: Array<ClueState>;
}

export interface RoundState {
  // categoryIdx and clueIdx are only present if a clue is opened.
  currentCategoryIdx?: number;
  currentClueIdx?: number;
  categoryStates: Array<CategoryState>;
}

export interface RoomState {
  type: 'RoomState';
  roundIdx: number;
  roundStates: Array<RoundState>;
  currentPlayerIdx: number;
}

export interface WaitingRoom {
  type: 'WaitingRoom';
}

export interface MemorableQuestion {
  category: string;
  question: string;
  answer: string;
  value: number;
  reason: string; // Why this question was selected as memorable
}

export interface RoomSummary {
  type: 'RoomSummary';
  // Preserve the original RoomState data for access in the winner screen
  originalState?: RoomState;
  // Memorable questions selected by the server
  memorableQuestions?: MemorableQuestion[];
}

export interface GeneratingQuestions {
  type: 'GeneratingQuestions';
  progress: number;
  message: string;
  category?: string;
  questionIndex?: number;
  startTime: number;
}

export type GameProgress =
  | WaitingRoom
  | RoomState
  | RoomSummary
  | GeneratingQuestions;

export interface FullRoomState {
  gameProgress: GameProgress;
}

export interface Message {
  id?: string;
  roomId: string;
  playerId: string;
  text: string;
  timestamp: any;
}

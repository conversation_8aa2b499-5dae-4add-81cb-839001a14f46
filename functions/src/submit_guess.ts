import {HttpsError} from "firebase-functions/https";
import {
  AnswerExplanation,
  Clue,
  ClueState,
  GameMode,
  Room,
  RoomState,
  Round,
} from "./resources";
import {SubmitGuessRequest} from "./services";
import {repeatedPingRoom} from "./ping_room";
import {LogType} from "./structured_log";
import {addStructuredLogEntry} from "./log_utils";
import levenshtein = require("js-levenshtein");
import {queryGeminiAPI} from "./gemini_api";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {extractLastJsonObject} from "./round_generator";
import {Transaction} from "firebase-admin/firestore";

const MAX_LEVENSHTEIN_DISTANCE = 2;

export function validate(
  roomData: Room,
  data: any,
  requestData: SubmitGuessRequest,
  nowTs: number
) {
  if (!data.auth.uid) {
    throw new HttpsError("permission-denied", "Unauthenticated request");
  }
  if (
    !requestData.roomId ||
    requestData.roundIdx === undefined ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined ||
    !requestData.playerId
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Room ID, RoundIdx, categoryIdx, clueIdx, and playerId are required"
    );
  }
  const currentPlayer: string | undefined = roomData.playerIds.find(
    (p) => p == requestData.playerId
  );
  if (!currentPlayer) {
    throw new HttpsError("invalid-argument", "PlayerId not part of this room");
  }

  if (requestData.guess === undefined || requestData.guess == "") {
    throw new HttpsError("invalid-argument", "Cannot submit empty string");
  }
  // Check if the game is in progress
  if (roomData.gameState.gameProgress.type !== "RoomState") {
    throw new HttpsError("failed-precondition", "Game not in progress");
  }
  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx != requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentCategoryIdx !=
    requestData.categoryIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid categoryIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentClueIdx !=
    requestData.clueIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid clueIdx");
  }
  const clueState = roomState.roundStates[requestData.roundIdx].categoryStates[
    requestData.categoryIdx
  ].clueStates[requestData.clueIdx] as ClueState;
  let answerStartTime = 0;
  if (clueState.answerStartTime != undefined) {
    answerStartTime = clueState.answerStartTime!;
  }

  let answerExpirationTime =
    answerStartTime + roomData.roomSettings!.answerDurationMillis;
  if (clueState.queueAnswerTurnIdx == 0) {
    answerExpirationTime += roomData.roomSettings!
      .initialThinkingDurationMillis;
  }
  const isPassed = requestData.guess == "<PASSING>";
  if (!isPassed && answerExpirationTime < nowTs) {
    throw new HttpsError("invalid-argument", "Time expired to give answer");
  }

  if (
    clueState.buzzedInPlayerQueue == undefined ||
    clueState.buzzedInPlayerQueue.length <= clueState.queueAnswerTurnIdx
  ) {
    throw new HttpsError("invalid-argument", "Need to buzz in to answer");
  }
  const expectedPlayer: string =
    clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];
  if (isPassed) {
    if (clueState.passedPlayers.some((p) => p == requestData.playerId)) {
      throw new HttpsError(
        "invalid-argument",
        "This player has already passed"
      );
    }
  } else if (expectedPlayer != requestData.playerId) {
    throw new HttpsError(
      "invalid-argument",
      "Only player " +
        roomData.playerIds.find((p) => p == expectedPlayer) +
        " is allowed to answer"
    );
  }
  if (clueState.showAnswerStartTime != undefined || clueState.clueComplete) {
    throw new HttpsError(
      "invalid-argument",
      "Cannot submit guess to a completed clue"
    );
  }
}

async function checkAnswerUsingLlm(
  category: string,
  question: string,
  correctAnswer: string,
  userAnswer: string
): Promise<AnswerExplanation> {
  if (userAnswer == "<PASSING>") {
    return {
      isCorrect: false,
      explanation: "User passed",
    };
  }
  const prompt = `
  In a trivia game, under the category "${category}", a player is asked: "${question}"
  The player responds: "${userAnswer}"
  The correct answer is: "${correctAnswer}"
  Is the player's answer acceptable as correct?  Explain your reasoning.
  Answer as a json
   {
    "isCorrect": {true/false},
    "explanation": "short_explanation_about_why_it_should_be_correct_or_wrong."
   }`;
  const resp: string = await queryGeminiAPI(prompt, "gemini-1.5-flash-8b");
  return JSON.parse(extractLastJsonObject(resp)!) as AnswerExplanation;
}

function checkAnswerNaive(correctAnswer: string, userAnswer: string) {
  return (
    levenshtein(correctAnswer.toLowerCase(), userAnswer.toLowerCase()) <
    MAX_LEVENSHTEIN_DISTANCE
  );
}

async function checkAnswer(
  roomData: Room,
  req: SubmitGuessRequest
): Promise<AnswerExplanation> {
  const resp = await admin
    .firestore()
    .collection("rooms")
    .doc(roomData.roomId)
    .collection("roomRoundData")
    .doc(`${req.roundIdx}`)
    .get();
  if (!resp.exists) {
    throw new HttpsError("not-found", "Clue not found");
  }
  const round = resp.data() as Round;
  const clue = round.categories[req.categoryIdx].clues[req.clueIdx];
  const categoryTitle = round.categories[req.categoryIdx].categoryTitle;
  if (checkAnswerNaive(clue.answer, req.guess)) {
    return {
      isCorrect: true,
      explanation: "Exact match.",
    };
  }
  const answerFromLlm = await checkAnswerUsingLlm(
    categoryTitle,
    clue.questionSentences.join("\n"),
    clue.answer,
    req.guess
  );
  console.log(`According to llm, answer is ${answerFromLlm}`);
  return answerFromLlm;
}

export async function modify(
  txn: Transaction,
  roomData: Room,
  clue: Clue,
  requestData: SubmitGuessRequest,
  answerExplanation: AnswerExplanation,
  nowTs: number
) {
  const roomState = roomData.gameState.gameProgress as RoomState;

  const newClueState: ClueState =
    roomState.roundStates[requestData.roundIdx].categoryStates[
      requestData.categoryIdx
    ].clueStates[requestData.clueIdx];

  // Initialize structuredClueLog if it doesn't exist
  if (!newClueState.structuredClueLog) {
    newClueState.structuredClueLog = {entries: []};
  }

  const playerName = roomData.playerInfos[
    roomData.playerIds.findIndex((p) => p == requestData.playerId)
  ].userName!;

  const isPassed: boolean = requestData.guess == "<PASSING>";
  const inBuzzQueue = newClueState.buzzedInPlayerQueue.some(
    (p) => p == requestData.playerId
  );

  if (inBuzzQueue) {
    newClueState.answerStartTime = 0;
  }

  if (newClueState.answerExplanations == undefined) {
    newClueState.answerExplanations = [];
  }
  newClueState.answerExplanations.push(answerExplanation);

  const currentPlayerIdx = roomData.playerIds.findIndex(
    (p) => p == requestData.playerId
  );

  // Create the appropriate structured log entry based on the answer type
  if (isPassed) {
    newClueState.passedPlayers.push(requestData.playerId!);

    // Create the pass log entry
    const passLogEntry = {
      type: LogType.PASS as LogType.PASS,
      timestamp: nowTs,
      playerId: requestData.playerId!,
      playerName: playerName,
    };

    // Only if a user passes after buzzing in, they should get -ve points.
    if (roomData.mode == GameMode.BUZZER && inBuzzQueue) {
      const pointsDeducted = clue.value / 2;
      roomData.playerInfos[currentPlayerIdx].score -= pointsDeducted;

      // Add points deducted to the log entry
      (passLogEntry as any).pointsDeducted = pointsDeducted;
    }

    // Add the structured log entry
    addStructuredLogEntry(newClueState, passLogEntry);
  } else if (!answerExplanation.isCorrect) {
    // Create the incorrect answer log entry
    const pointsDeducted = clue.value / 2;
    roomData.playerInfos[currentPlayerIdx].score -= pointsDeducted;

    const incorrectLogEntry = {
      type: LogType.ANSWER_INCORRECT as LogType.ANSWER_INCORRECT,
      timestamp: nowTs,
      playerId: requestData.playerId!,
      playerName: playerName,
      guess: requestData.guess,
      pointsDeducted: pointsDeducted,
    };

    // Add the structured log entry
    addStructuredLogEntry(newClueState, incorrectLogEntry);
  } else {
    // Correct answer
    newClueState.showAnswerStartTime = nowTs;
    newClueState.showAnswer = true;
    newClueState.answeredByPlayerId = requestData.playerId;

    let pointsAwarded = clue.value;
    if (newClueState.hintOpened) {
      pointsAwarded /= 2;
    }

    roomData.playerInfos[currentPlayerIdx].score += pointsAwarded;

    if (roomData.roomSettings?.gameMode === GameMode.BUZZER) {
      roomState.currentPlayerIdx = currentPlayerIdx;
    }

    // Create the correct answer log entry
    const correctLogEntry = {
      type: LogType.ANSWER_CORRECT as LogType.ANSWER_CORRECT,
      timestamp: nowTs,
      playerId: requestData.playerId!,
      playerName: playerName,
      guess: requestData.guess,
      pointsAwarded: pointsAwarded,
    };

    // Add the structured log entry
    addStructuredLogEntry(newClueState, correctLogEntry);

    // Also add a show answer log entry
    const showAnswerLogEntry = {
      type: LogType.SHOW_ANSWER as LogType.SHOW_ANSWER,
      timestamp: nowTs,
      answer: clue.answer,
    };

    // Add the structured log entry
    addStructuredLogEntry(newClueState, showAnswerLogEntry);
  }
  console.log(newClueState);
  return await repeatedPingRoom(
    txn,
    roomData,
    {roomId: requestData.roomId, playerId: requestData.playerId!},
    nowTs
  ); // Commit the changes
}

export const submitGuess = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as SubmitGuessRequest;
    const db = admin.firestore();
    if (!req.playerId && data.auth.uid) {
      req.playerId = data.auth.uid!;
    }
    try {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomData = (await roomRef.get()).data() as Room;
      validate(roomData, data, req, Date.now());
      const isCorrect = await checkAnswer(roomData, req);
      db.runTransaction(async (txn) => {
        const roomDoc = await txn.get(roomRef);
        const clueRef = roomRef
          .collection("roomRoundData")
          .doc(`${req.roundIdx}`);
        const clueDoc = await txn.get(clueRef);
        if (!roomDoc.exists) {
          throw new HttpsError("not-found", "Room not found");
        }
        const roomData = roomDoc.data() as Room;
        const clueData = clueDoc.data() as Round;
        const nowTs = Date.now();
        validate(roomData, data, req, 0);
        const roomState = roomData.gameState.gameProgress as RoomState;
        await txn.set(
          roomRef,
          await modify(
            txn,
            roomData,
            clueData.categories[
              roomState.roundStates[req.roundIdx].currentCategoryIdx!
            ].clues[roomState.roundStates[req.roundIdx].currentClueIdx!],
            req,
            isCorrect,
            nowTs
          )
        );
      });
    } catch (error) {
      console.error("Error selecting clue:", error);
      if (error instanceof HttpsError) throw error;
      throw new HttpsError("internal", "Internal Server Error");
    }
  }
);

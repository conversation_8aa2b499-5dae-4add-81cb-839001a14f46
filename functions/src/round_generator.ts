import {LlmAgent, Round} from "./resources";
import {queryGeminiAPI} from "./gemini_api";
import {queryOpenAI} from "./openai_api";
import {HttpsError} from "firebase-functions/https";
import {queryDeepSeekAI} from "./deepseek_api";

interface LlmResponse {
  broadArea: string;
  categoryTitle: string;
  categoryDescription: string;
  questions: {
    questionSentences: Array<string>;
    questionHTML: string;
    hint: string;
    answer: string;
    value: number;
    detailedFactsAboutAnswer: Array<string>;
    answerExplanation: Array<string>;
  }[];
}

export function extractLastJsonObject(text: string): string | null {
  const lastJsonIndex = text.lastIndexOf("```json");

  if (lastJsonIndex === -1) {
    return null; // No JSON block found
  }

  const closingIndex = text.indexOf("`", lastJsonIndex + 7); // +7 to skip "`json"

  if (closingIndex === -1) {
    return null; // No closing tag found
  }

  return text.substring(lastJsonIndex + 7, closingIndex).trim();
}
const fakeRoundResponse = "";

export async function runPrompt(prompt: string, llmAgent: LlmAgent) {
  switch (llmAgent.provider) {
  case "Gemini":
    return queryGeminiAPI(prompt, llmAgent.modelName);
  case "OpenAI":
    return queryOpenAI(prompt, llmAgent.modelName);
  case "DeepSeekAI":
    return queryDeepSeekAI(prompt, llmAgent.modelName);
  case "Fake":
    return fakeRoundResponse;
  }
}

export async function generateRound(
  broadAreas: string[],
  numQuestions: number,
  numCategories: number,
  llmAgent: LlmAgent
): Promise<Array<Round>> {
  try {
    const prompt = `
You are a trivia game master. Your task is to generate a set of **highly creative and engaging** trivia categories and questions based on user-provided broad areas.  **Focus on forging unexpected connections between topics and developing unique perspectives within each broad area.** You will be given a number of categories to generate, and it may be more than the number of broad areas. Distribute the categories across broad areas as evenly as possible. It is encouraged to combine two broad areas into a single category when appropriate.

**1. Category Generation:**

*   **Input:**
    *   A list of broad areas from the user (e.g., "Chemistry", "Movies", "History", "Physics").
    *   The number of categories to generate (numCategories).
*   **Process:**
    *   Determine how to distribute the numCategories across the provided broad areas as evenly as possible. For instance, if there are 3 broad areas and 5 categories, you might generate 2 categories for the first two broad areas and 1 for the last.
    *   **Brainstorm:** Before generating categories, quickly list 5-10 diverse category themes or concepts that could be applied to the given broad areas. This is just a brainstorming step, and you don't need to use all of these ideas, but it will help you generate more original categories.
    *   For each category:
        *   Choose one or two broad areas to draw from. Combining two broad areas into a single category is encouraged if it creates a unique and interesting intersection (e.g., "Culinary Chemistry" for the chemical reactions involved in cooking).
        *   Generate a creative, engaging, and unique category title that represents a specific subtopic or **unique perspective** within the chosen broad area(s). **Strive for unexpected connections that spark curiosity.** **Consider if a linguistic pattern could be used to form the basis of an interesting category.**
        *   Provide a concise description (1-2 sentences) explaining the focus or theme of the category, especially how it relates to the chosen broad area(s).
*   **Output:** A JSON list of categories, each with the associated broad area(s), category title, and category description.

**2. Question Generation:**

*   **Input:** The JSON list of categories from Step 1.
*   **Process:** For each category, generate a set of trivia questions with the following properties:
    *   **Difficulty Progression:** Order questions from easiest to hardest. The easiest should be answerable with basic familiarity of the category, while the hardest requires deeper knowledge or deduction but should still be solvable with the provided hint.
    *   **Verifiable Facts:** Base questions on verifiable facts.
    *   **Reasoning & Deduction:** Questions should encourage reasoning and deduction, not just recall. The answer should not be directly stated or implied within the question itself.
    *   **Surprising & Engaging:** Prioritize interesting, surprising, or counterintuitive facts. Aim for "aha!" moments.
    *   **Uniqueness:** Ensure that no two questions in the entire set are about the same thing.
    *   **Hint:** Provide a concise hint that guides the user's reasoning without revealing the answer. The hint should act as a stepping stone, prompting deduction.
    *   **Multiple Clues in Question:** Include more than one clue within each question to provide multiple angles for deduction.
    *   **Short, Unambiguous Answers:** Answers should be short and specific, avoiding ambiguity.
    *   **Easy Validation:** Answers should be easily verifiable using simple string comparison techniques (like Levenshtein distance).
    *   **Accessibility:** Target a general audience with a casual interest in the category. Avoid overly specialized or technical aspects unless they are widely known within the general public's understanding of the category.
    *   **Category Specificity:** Each question **must** directly relate to the specific subtopic, niche, or unique perspective described in the category. **Reject any question that, while related to the broad area, could apply to many other categories and does not specifically address the unique focus of the defined category.**
    *   **Question Variety:** Vary question formats to maintain engagement. Consider formats like:
        *   Historical Context/Origins
        *   Functional/Impact Descriptions
        *   Unexpected Connections
        *   Hypothetical Scenarios
        *   Etymology connections
        *   Interesting analogies
    *   **Emphasis on the Answer Sought:** Clearly specify what the question is looking for in the answer.
    *   **Answer Absence:** Ensure the answer isn't directly present in the question or hint.
*   **Output:** A JSON structure (detailed below) containing the questions and hints for each category.

**3. Strict Requirements (for both Category and Question Generation):**

*   **No Hallucination:** Only include information you are 100% sure of. Double-check facts.
*   **Logical Consistency:** Ensure all elements (category titles, descriptions, questions, hints, answers) are factually and logically consistent.
*   **Focus on Deduction:** The hint and question should work together to lead to a single, deducible answer.
*   **Prioritize Surprising Origins, Early Products, or Unexpected Connections:** Emphasize these aspects when crafting categories and questions.

**Output Format (JSON):**
[
  {
    "broadAreas": ["BroadArea1"], 
    "categoryTitle": "CategoryTitle1",
    "categoryDescription": "A brief description of what the category is about.",
    "questions": [
      {
        "answer": "Some answer relevant to Category 1",
        "detailedFactsAboutAnswer": [
          "5 short, dense, unique, interesting facts about answer. (Not shown to the player, used as a scratchpad.)",
          "...",
          "...",
          "...",
          "..."
        ],
        "value": 100,
        "question": "Question drawn from detailedFactsAboutAnswer, incorporating multiple clues. Explicitly end the question with what you're looking for as the answer.",
        "hint": "Hint that guides deduction without revealing the answer."
      },
      {
        "answer": "Some other answer relevant to Category 1",
        "detailedFactsAboutAnswer": ["...", "...", "...", "...", "..."],
        "value": 200,
        "question": "...",
        "hint": "..."
      },
      // ... more questions for this category, increasing in value/difficulty
    ]
  },
  {
    "broadAreas": ["BroadArea2", "BroadArea3"],
    "categoryTitle": "CategoryTitle2",
    "categoryDescription": "...",
    "questions": [ /* ... */ ]
  },
  // ... more categories
]

**Creative Inspiration:**

Here are some general types of creative thinking that can lead to interesting trivia categories:

*   **Wordplay:** Explore anagrams, palindromes, homophones, puns, redefining words over time, portmanteaus, rhyming slang, spoonerisms, and other linguistic relationships.
    *   **Linguistic Patterns:**  Develop categories based on words that share specific prefixes, suffixes, or other structural patterns.
*   **Conceptual Connections:** Link seemingly disparate ideas or fields in surprising ways, such as through six degrees of separation, the butterfly effect, technology convergence, or inventions by accident.
*   **Historical Twists:** Focus on lesser-known origins, surprising early versions of things, unexpected historical influences, unsung heroes/heroines, alternative histories, debunking myths, or lost technologies.
*   **Genre Bending:** Combine elements of different genres or artistic styles in a trivia context, such as sci-fi meets history, fantasy in the real world, or historical fiction mashups.
*   **"What If?" Scenarios:** Pose hypothetical questions that challenge conventional understanding, including technological, geographical, biological, or cultural "what ifs."
*   **Rule Tweaking:** Take a familiar concept and apply a unique constraint or twist to it, such as inverse categories, geographic limitations, or time-based restrictions.
*   **Pop Culture Mashups:** Blend different pop culture universes or characters in unexpected ways, such as crossover episodes, character role reversals, or real-world pop culture impacts.
*   **Sensory Exploration:** Consider categories involving synesthesia, the sounds of history, or the role of scents in different contexts.
*   **Mathematical & Logical Puzzles:** Explore famous paradoxes, number patterns, or optical illusions.
*   **The Mundane Made Extraordinary:** Focus on everyday object origins, the science of hobbies, or occupational oddities.
*   **Art and Music:** Consider categories based on artistic movements, musical borrowing, or one-hit wonders.

Generate a total of numCategories=${numCategories} categories, with numQuestions=${numQuestions} questions per category, using the following broad areas: ${broadAreas.join(
  ", "
)}. 
Distribute the categories across the broad areas as evenly as possible, and feel free to combine multiple broad areas into a single category if it makes for an interesting combination.
`;

    const rawResponse = await runPrompt(prompt, llmAgent);

    const parsedResponse = JSON.parse(
      extractLastJsonObject(rawResponse)!
    ) as LlmResponse[];

    const rounds: Round[] = [
      {
        categories: parsedResponse.map((categoryResponse) => {
          return {
            categoryTitle: categoryResponse.categoryTitle,
            categoryDescription: categoryResponse.categoryDescription,
            clues: categoryResponse.questions.map((question, _index) => ({
              questionSentences: question.questionSentences,
              questionHTML: "",
              hint: question.hint,
              answer: question.answer,
              value: question.value,
              detailedFactsAboutAnswer: [],
              hintBlanks: createHintBlanksFromAnswer(question.answer, 30),
            })),
          };
        }),
      },
    ];

    return Promise.resolve(rounds);
  } catch (error) {
    console.log(error);
    throw new HttpsError(
      "internal",
      `Error generating questions for categories "${broadAreas}".`
    );
  }
}

export async function generateRoundSimple(
  broadAreas: string[],
  numQuestions: number,
  numCategories: number,
  llmAgent: LlmAgent
): Promise<Array<Round>> {
  numCategories = broadAreas.length;
  try {
    const prompt = `
You are a trivia game master. Your task is to generate a set of **highly creative and engaging** trivia questions based on user-provided broad areas. Your persona is that of a **slightly snarky, very witty, and slightly condescending game show host** – inject humor and playful derision where appropriate, making the questions enjoyable and memorable for the audience. **Pay very close attention to the category title. If a category title IMPLIES or EXPLICITLY STATES a specific question format or structural constraint (like "Guess movie with 5 words" or "Questions in haiku form"), you MUST adhere to that format strictly. However, for categories WITHOUT format-defining titles, generate a VARIETY of question formats to maximize engagement.**

**1. Question Generation:**

* **Audience Deduction and Difficulty Grading:** Analyze the provided categoryTitles to infer the likely background and expertise of the players (e.g., nationality, profession, interests). Based on this inferred audience, grade the difficulty of the questions. For example, if categories include "Bollywood movies," "Fun math facts," and "Trump tariffs," infer the audience likely includes individuals interested in Indian culture, engineering or science, and American politics. In this case, don't ask questions that would end up being too simple for people from that background. Adjust question difficulty to be appropriate for such a group. **As a fallback, if you cannot deduce the audience type from the categories, assume the audience consists of Indian computer science engineers who grew up in North India and grade the difficulty of the questions accordingly.**
* **Inject Fun and Engagement:** Beyond just facts, weave in interesting trivia, surprising connections, or witty observations that the target audience would find entertaining or delightful. Make the questions memorable and shareable. Remember your **snarky persona** when phrasing questions and hints.
* **Process:** For each category, generate a set of trivia questions with the following properties:
    * **Difficulty Progression:** Order questions from hardest to easiest.
    * **Verifiable, Obscure Facts:** Base questions on verifiable facts.
    * **Mandatory Deduction & Lateral Thinking**: Questions must require reasoning and deduction.
    * **Surprising & Engaging:** Prioritize interesting, surprising, or counterintuitive facts. **Think about how specific question formats or structures could make questions more engaging, *especially in categories where a format constraint is defined*.**
    * **Uniqueness:** Ensure question uniqueness within the set.
    * **Hint:** Provide a concise, deduction-guiding hint that **never contains the answer**. Deliver the hint in your **snarky game master voice**.
    * **Multiple Clues in Question:** Include varied clues providing different facets of information.
    * **Short, Unambiguous Answers:** Answers should be short and specific.
    * **Easy Validation:** Answers should be easily verifiable.
    * **Category Specificity:** Each question **must** DIRECTLY relate to the specific subtopic, niche, or format implied by the **category title**.
        * **Format-Defined Categories:** **If the category title strongly suggests a question format or structural constraint (e.g., "Guess movie with 5 words"), then ALL questions in that category MUST strictly adhere to that format.** For "Guess movie with 5 words": "questionHTML" should CONTAIN **ONLY** a question structured to elicit a 5-word movie title answer, and a final, HTML-formatted sentence explicitly asking to guess the movie in 5 words. **REJECT questions with extra descriptive text or elements that violate the 5-word constraint beyond the core question and final sentence.** Frame these within your **snarky persona**.
        * **General Categories:** For categories **without** explicit format-defining titles, generate questions with varied formats (Historical Context, Functional Descriptions, Analogies, etc.). Inject your **snarky tone** into the phrasing.
    * **Question Variety:**
        * **Inline CSS:** Include inline-CSS in the question to make it more presentable. Please add 'style="width=100%"' to each div, and center things in it when appropriate.
        * **Emoji divs: MANDATORY WRAPPING:** **Whenever you include emojis in the \`questionHTML\`, you MUST wrap them in a \`<div>\` element with the class "emoji". This is a *strict requirement*. Every single emoji must be enclosed like this: \`<div class="emoji">😊</div>\`. Do not include emojis directly without this wrapper. This is crucial for consistent styling and correct rendering.**
        * **General Categories:** Vary question formats significantly to maintain engagement. Use a wide range of formats: Riddles with constraints, Historical Context, Functional Descriptions, Analogies, etc. **Aim for a rich mix of question types within these categories. Don't force inject these ideas in the question. Only use them if it makes sense.**
    * **Emphasis on the Answer Sought:**
        * Clearly specify what you're looking for, elegantly integrated into the final sentence, and **easily identifiable in HTML, even within format-constrained categories.** Phrase this in your **snarky game master voice**.
        * **CRITICAL: SINGLE QUESTION FOCUS:** **It's absolutely critical that each question contain at-most *ONE SINGLE, CLEAR QUESTION* focused on eliciting *ONE SPECIFIC PIECE OF INFORMATION*. AVOID GENERATING "DOUBLE QUESTIONS" OR ASKING THE SAME THING TWICE IN DIFFERENT WORDS WITHIN A SINGLE QUESTION ENTRY.** For example, **REJECT** questions that look like this (this is an *example of what NOT to do*):

          \`\`\`html
          <div>Known for X and Y, this person does Z. What is their profession?
          Name this person's job.</div>
          \`\`\`
          This is redundant and bad. Instead, ask **ONE clear question** like:

          \`\`\`html
          <div>Known for X and Y, this person does Z. <p>What is their profession?</p></div>
          \`\`\`
          Or:
          \`\`\`html
          <div>Known for X and Y, this person does Z. <p>Name their job.</p></div>
          \`\`\`
          **Focus on conciseness and clarity. Only ask for ONE specific piece of information ONCE. Deliver the question in your snarky persona.**

    * **Strict Answer Absence:** The answer **must not** be stated, implied, or easily deducible.
    * **Question Integrity Check:** Critically evaluate each question. **Strictly enforce format for format-defined categories. Ensure variety and creativity for general categories. Make sure the question and hints are separate. ENSURE SINGLE QUESTION FOCUS. Maintain your snarky tone throughout.**

* **Output:** A JSON structure (detailed below).

**2. Strict Requirements:**

* **No Hallucination:** 100% factually accurate and verifiable.
* **Logical Consistency:** All elements must be logically consistent.
* **Focus on Deduction:** Hint and question must lead to a single, deducible answer.
* **Prioritize Surprising Origins, Early Products, Unexpected Connections, and Metaphorical Relationships:** Incorporate these, and **consider how specific formats enhance engagement or suit a category's theme. Deliver these with a snarky twist.**
* **Category-Specific Formatting:** **ABSOLUTELY adhere to format constraints for format-defined categories. Maintain question variety for general categories. Inject snarkiness into the presentation.**

* **Question Structure (HTML Formatting & Format Constraints):**
    * The "questionHTML" field **must contain valid and semantically meaningful HTML.**
        * **Format-Defined Categories (e.g., "Guess movie with 5 words"):** "questionHTML" **MUST** primarily consist of content structured according to the defined format, adhering strictly to the format constraints (e.g., question designed for a 5-word answer, structured with "<div>" for question elements, "<p>" for final sentence). **AND IT MUST CONTAIN ONLY ONE QUESTION.** Frame these within your **snarky persona**.
        * **General Categories:** Use HTML to structure content creatively. Employ elements like "<div>", "<p>", "<span>", "<strong>", "<em>", lists, etc. **Consider format or structural elements where they enhance the question, but they are not mandatory unless the category dictates it. AGAIN: ENSURE EACH \`questionHTML\` CONTAINS ONLY ONE QUESTION. Deliver the question content with snark.**
        * **NO FONT SIZING:** Do NOT include any font-size styles in your HTML. The game interface handles all font sizing automatically for optimal display on different devices.
        * **Emoji DIV Reinforcement:** **To be absolutely clear, every emoji you include in \`questionHTML\` *must* be wrapped in \`<div class="emoji">\`. For example: \`<div class="emoji">💡</div>\`. This is a mandatory formatting rule.**
        * Maintain clean, semantic, and concise HTML.
        * Make the final sentence asking for the answer **clearly identifiable in HTML, even within format-constrained categories. Make sure that this is the *only* sentence ending with a question mark in the entire question. DOUBLE CHECK: ONLY ONE QUESTION MARK IN THE ENTIRE \`questionHTML\`, AND IT MUST BE AT THE END OF THE SINGLE, CLEAR QUESTION.** Deliver this final sentence with **snarky flair**.
        * "questionHTML" field contains the complete HTML question. **AND IT CONTAINS ONLY ONE QUESTION. REVIEW CAREFULLY.**
        * **Valid JSON:** It's CRITICAL that you make the json valid, and escape all the necessary characters. If the JSON is invalid, the JSON parser WILL FAIL!

**Output Format (JSON):**
[
  {
    "categoryTitle": "...",
    "categoryDescription": "...", // Inject snarky commentary about the category here!
    "questions": [
      {
        "answer": "...",
        "value": 500,
        "questionHTML": "Some html of a question. Make sure to include styles within the html to make it look good. Deliver this with snark.",
        "answerExplanation": ["Explanation...", "Facts...", ... up to 5], // Ensure explanations also have a touch of snark.
        "hint": "..." // Hints should be snarky too.
      },
      {
        "answer": "...",
        "value": 400,
        "questionHTML": "Some html of a question. You may include basic styling like centering, but do NOT include font-size styles. Deliver this with snark.",
        "answerExplanation": ["Explanation...", "Facts...", ... up to 5], // Ensure explanations also have a touch of snark.
        "hint": "..." // Hints should be snarky too.
      }
      ...
    ]
  },
  {...},
  ...
],


Please generate ${numCategories} categories, with numQuestions=${numQuestions} questions per category, using the following category titles: ${broadAreas.join(
  ", "
)}.
`;

    const rawResponse = await runPrompt(prompt, llmAgent);

    const parsedResponse = JSON.parse(
      extractLastJsonObject(rawResponse)!
    ) as LlmResponse[];

    const rounds: Round[] = [
      {
        categories: parsedResponse.map((categoryResponse) => {
          return {
            broadAreas: [categoryResponse.categoryTitle],
            categoryTitle: categoryResponse.categoryTitle,
            categoryDescription: categoryResponse.categoryDescription,
            clues: [
              ...categoryResponse.questions.map((question, _index) => ({
                questionSentences: [],
                questionHTML: question.questionHTML,
                hint: question.hint,
                answer: question.answer,
                value: question.value,
                detailedFactsAboutAnswer: question.answerExplanation,
                hintBlanks: createHintBlanksFromAnswer(question.answer, 30),
              })),
            ].reverse(),
          };
        }),
      },
    ];

    return Promise.resolve(rounds);
  } catch (error) {
    console.log(error);
    throw new HttpsError(
      "internal",
      `Error generating questions for categories "${broadAreas}".`
    );
  }
}

export function createHintBlanksFromAnswer(
  answer: string,
  visiblePerc: number
): string {
  // 1. Handle invalid input:
  if (visiblePerc < 0 || visiblePerc > 100) {
    throw new Error("visiblePerc must be between 0 and 100");
  }

  // 2. Create an array of characters, replacing letters/numbers with underscores:
  const answerChars = answer.split("");
  const hintBlanks = answerChars.map((char) => {
    if (/[a-zA-Z0-9]/.test(char)) {
      return "_";
    } else {
      return char; // Keep spaces, punctuation as they are
    }
  });

  // 3. Calculate the number of characters to reveal:
  const numCharsToReveal = Math.round(
    answer.replace(/[^a-zA-Z0-9]/g, "").length * (visiblePerc / 100)
  );

  // 4. Randomly select characters to reveal:
  const indicesToReveal: Array<number> = [];
  let count = 0;
  while (count < numCharsToReveal) {
    const randomIndex = Math.floor(Math.random() * answerChars.length);
    // Only consider letters/numbers for potential revealing:
    if (
      /[a-zA-Z0-9]/.test(answerChars[randomIndex]) &&
      !indicesToReveal.includes(randomIndex)
    ) {
      indicesToReveal.push(randomIndex);
      count++;
    }
  }

  // 5. Reveal the selected characters:
  for (const index of indicesToReveal) {
    hintBlanks[index] = answerChars[index];
  }

  // 6. Return the hint string:
  return hintBlanks.join("");
}
